{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Inventory Management System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f9;
            line-height: 1.6;
        }

        .navbar {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        /* Modern Fixed Sidebar */
        .modern-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            z-index: 1000;
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .modern-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .modern-sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .modern-sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .brand {
            display: flex;
            align-items: center;
            color: #fff;
        }

        .brand-icon {
            font-size: 24px;
            margin-right: 12px;
            color: #4fc3f7;
        }

        .brand-text {
            font-size: 20px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
        }

        .sidebar-content {
            padding: 20px 0;
        }

        .user-profile {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            overflow: hidden;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-avatar i {
            color: #fff;
            font-size: 20px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            color: #fff;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 2px;
        }

        .user-role {
            color: #b0bec5;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-section {
            margin-bottom: 25px;
        }

        .nav-title {
            color: #78909c;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 20px 10px;
            margin-bottom: 10px;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 2px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #cfd8dc;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.08);
            color: #fff;
            border-left-color: #4fc3f7;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: rgba(79, 195, 247, 0.15);
            color: #4fc3f7;
            border-left-color: #4fc3f7;
        }

        .nav-icon {
            width: 20px;
            text-align: center;
            margin-right: 15px;
            font-size: 16px;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .external-icon {
            font-size: 12px;
            opacity: 0.7;
        }

        /* Main content adjustment for fixed sidebar */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .main-content-no-sidebar {
            margin-left: 0;
            min-height: 100vh;
            background: #f8f9fa;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .modern-sidebar {
                transform: translateX(-100%);
            }

            .modern-sidebar.show {
                transform: translateX(0);
            }

            .main-content,
            .main-content-no-sidebar {
                margin-left: 0;
            }
        }

        .main-content {
            padding: 30px;
            min-height: calc(100vh - 76px);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #3498db, #5dade2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #5dade2);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .table th {
            background-color: #2c3e50;
            color: white;
            border: none;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
        }

        .modal-content {
            border-radius: 15px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, #3498db, #5dade2);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .badge {
            border-radius: 20px;
            padding: 8px 15px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                position: fixed;
                top: 76px;
                left: 0;
                width: 250px;
                z-index: 1000;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                padding: 15px;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="btn btn-outline-light d-lg-none me-2" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-boxes me-2"></i>Inventory Pro
            </a>
            
            <div class="navbar-nav ms-auto">
                {% if user.is_authenticated %}
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span id="username">{{ user.get_full_name|default:user.username }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'user_profile' %}"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="post" action="{% url 'logout' %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="dropdown-item border-0 bg-transparent w-100 text-start">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
                <div class="nav-item ms-3">
                  <form method="post" action="{% url 'logout' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger text-white fw-bold px-3 py-2 d-block d-lg-inline-block w-100 w-lg-auto text-center border-0">
                      <i class="fas fa-sign-out-alt me-1"></i> Logout
                    </button>
                  </form>
                </div>
                {% else %}
                <div class="nav-item">
                    <a class="nav-link text-white" href="/login/">
                        <i class="fas fa-sign-in-alt me-1"></i>Login
                    </a>
                </div>
                {% endif %}


            </div>
        </div>
    </nav>

    <div class="app-container">
        {% if user.is_authenticated %}
            <!-- Sidebar -->
            <!-- Modern Fixed Sidebar -->
            <nav class="modern-sidebar" id="sidebar">
                <div class="sidebar-header">
                    <div class="brand">
                        <i class="fas fa-cube brand-icon"></i>
                        <span class="brand-text">InventoryPro</span>
                    </div>
                    <button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="sidebar-content">
                    {% if user.is_authenticated %}
                        <!-- User Profile Section -->
                        <div class="user-profile">
                            <div class="user-avatar">
                                {% if user.profile.avatar %}
                                    <img src="{{ user.profile.avatar.url }}" alt="Profile">
                                {% else %}
                                    <i class="fas fa-user"></i>
                                {% endif %}
                            </div>
                            <div class="user-info">
                                <div class="user-name">{{ user.first_name|default:user.username }}</div>
                                <div class="user-role">Store Owner</div>
                            </div>
                        </div>

                        <!-- Navigation Menu -->
                        <div class="nav-section">
                            <div class="nav-title">Dashboard</div>
                            <ul class="nav-menu">
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'dashboard' %}">
                                        <i class="fas fa-tachometer-alt nav-icon"></i>
                                        <span class="nav-text">Dashboard</span>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <div class="nav-section">
                            <div class="nav-title">Store Management</div>
                            <ul class="nav-menu">
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'private_seller_store' %}">
                                        <i class="fas fa-store nav-icon"></i>
                                        <span class="nav-text">My Private Store</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/seller/{{ user.id }}/store/" target="_blank">
                                        <i class="fas fa-external-link-alt nav-icon"></i>
                                        <span class="nav-text">View Public Store</span>
                                        <i class="fas fa-external-link-alt external-icon"></i>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/stores/">
                                        <i class="fas fa-store-alt nav-icon"></i>
                                        <span class="nav-text">Manage Stores</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'list_authorized_stores' %}">
                                        <i class="fas fa-user-shield nav-icon"></i>
                                        <span class="nav-text">Authorized Stores</span>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <div class="nav-section">
                            <div class="nav-title">Inventory</div>
                            <ul class="nav-menu">
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'manage_goods_inventory' %}">
                                        <i class="fas fa-boxes nav-icon"></i>
                                        <span class="nav-text">Inventory Management</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'goods_list_create' %}">
                                        <i class="fas fa-plus-circle nav-icon"></i>
                                        <span class="nav-text">Add Product</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/goods/">
                                        <i class="fas fa-box-open nav-icon"></i>
                                        <span class="nav-text">All Products</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/categories">
                                        <i class="fas fa-tags nav-icon"></i>
                                        <span class="nav-text">Categories</span>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <div class="nav-section">
                            <div class="nav-title">Financial</div>
                            <ul class="nav-menu">
                                <li class="nav-item">
                                    <a class="nav-link" href="/receipts">
                                        <i class="fas fa-receipt nav-icon"></i>
                                        <span class="nav-text">Receipts</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/invoices">
                                        <i class="fas fa-file-invoice nav-icon"></i>
                                        <span class="nav-text">Invoices</span>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <div class="nav-section">
                            <div class="nav-title">Other</div>
                            <ul class="nav-menu">
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'property_management' %}">
                                        <i class="fas fa-building nav-icon"></i>
                                        <span class="nav-text">Property Management</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/public/goods/">
                                        <i class="fas fa-shopping-cart nav-icon"></i>
                                        <span class="nav-text">Public Marketplace</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </nav>
        {% endif %}

            <!-- Main Content -->
            <main class="{% if user.is_authenticated %}main-content{% else %}main-content-no-sidebar{% endif %}">
                <!-- Loading indicator -->
                <div class="loading" id="loadingIndicator">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading...</p>
                </div>

                <!-- Alert container -->
                <div id="alertContainer"></div>

                {% block content %}{% endblock %}
            </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Utility functions
        function showLoading() {
            document.getElementById('loadingIndicator').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingIndicator').style.display = 'none';
        }

        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();
            
            const alertHTML = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHTML);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        function getAlertIcon(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // API request helper
        async function apiRequest(url, options = {}) {
            showLoading();
            
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            };

            // Merge options
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            try {
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                
                hideLoading();
                
                if (!response.ok) {
                    throw new Error(data.error || `HTTP error! status: ${response.status}`);
                }
                
                return data;
            } catch (error) {
                hideLoading();
                console.error('API Request Error:', error);
                showAlert(error.message, 'danger');
                throw error;
            }
        }

        // Authentication functions
        async function checkAuth() {
            try {
                const user = await apiRequest('/auth/me/');
                currentUser = user;
                document.getElementById('username').textContent = user.username;
                return user;
            } catch (error) {
                // Redirect to login if not authenticated
                if (window.location.pathname !== '/login/' && 
                    window.location.pathname !== '/register/' &&
                    window.location.pathname !== '/forgot-password/') {
                    window.location.href = '/login/';
                }
                return null;
            }
        }

// Logout function removed - now using proper Django forms


        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !sidebarToggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication on protected pages
            const protectedPages = ['/dashboard/', '/stores/', '/inventories/', '/goods/', '/receipts/', '/profile/'];
            const currentPath = window.location.pathname;
            
            if (protectedPages.some(page => currentPath.startsWith(page))) {
                checkAuth();
            }

            // Set active sidebar link
            const currentUrl = window.location.pathname;
            const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
            sidebarLinks.forEach(link => {
                if (link.getAttribute('href') === currentUrl) {
                    link.classList.add('active');
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
