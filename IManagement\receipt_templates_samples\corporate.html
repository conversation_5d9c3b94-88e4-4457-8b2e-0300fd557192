<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Corporate Receipt</title>
  <style>
    body {
      font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      max-width: 700px;
      margin: 30px auto;
      background: #f9f9f9;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 0 12px rgba(0,0,0,0.1);
      color: #34495e;
    }
    header {
      border-bottom: 3px solid #2980b9;
      padding-bottom: 15px;
      margin-bottom: 30px;
    }
    h1 {
      margin: 0;
      color: #2980b9;
      font-weight: 700;
    }
    .company {
      font-size: 1.1em;
      margin-top: 8px;
      color: #7f8c8d;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 35px;
    }
    th, td {
      padding: 12px 15px;
      border-bottom: 1px solid #ccc;
      text-align: left;
    }
    th {
      background-color: #d6eaf8;
      font-weight: 700;
      color: #2c3e50;
    }
    .total {
      font-size: 1.4em;
      font-weight: 700;
      text-align: right;
      color: #27ae60;
    }
    footer {
      display: flex;
      justify-content: space-between;
      font-size: 0.9em;
      color: #95a5a6;
    }
    .signature, .stamp {
      border-top: 1px solid #bdc3c7;
      width: 200px;
      padding-top: 5px;
      font-weight: 600;
      color: #7f8c8d;
      text-align: center;
    }
  </style>
</head>
<body>
  <h1>Receipt</h1>
  <div class="company-info">
    <p>{{ company_name }}</p>
    <p>Thank you for your purchase!</p>
  </div>
  <table>
  <thead>
    <tr>
      <th>Item</th>
      <th>Qty</th>
      <th>Unit Price</th>
      <th>Price</th>
    </tr>
  </thead>
  <tbody>
    {% for item in items %}
    <tr>
      <td>{{ item.name }}</td>
      <td>{{ item.quantity }}</td>
      <td>{{ item.unit_price }}</td>
      <td>{{ item.total_price }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>

  <p class="total">Total: {{ total_price }}</p>
  <div class="footer">
    <p>Signature:</p>
    {% if signature_url %}
      <img src="{{ signature_url }}" alt="Signature" />
    {% else %}
      <p>____________________</p>
    {% endif %}

    <p>Stamp:</p>
    {% if stamp_url %}
      <img src="{{ stamp_url }}" alt="Stamp" />
    {% else %}
      <p>____________________</p>
    {% endif %}
  </div>
</body>
</html>
