<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse Goods - InventoryPro</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --dark-color: #1f2937;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            background: #f8fafc;
        }

        .navbar {
            background: rgba(255,255,255,0.98) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .hero-banner {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 80px 0 60px;
            position: relative;
            overflow: hidden;
        }

        .hero-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 500"><polygon fill="rgba(255,255,255,0.1)" points="0,0 1000,300 1000,500 0,200"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .main-content {
            margin-top: -40px;
            position: relative;
            z-index: 3;
        }

        .filter-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }

        .filter-card .card-body {
            padding: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .product-image {
            height: 220px;
            object-fit: cover;
            border-radius: 0;
        }

        .product-placeholder {
            height: 220px;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .price-tag {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .store-badge {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .btn-view-details {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-view-details:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
            color: white;
        }

        .btn-contact {
            border-radius: 12px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-contact:hover {
            transform: translateY(-1px);
        }

        .filter-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            color: white;
        }

        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .no-results i {
            font-size: 4rem;
            color: #d1d5db;
            margin-bottom: 1.5rem;
        }

        /* Modal Enhancements */
        .modal-content {
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            border-radius: 20px 20px 0 0;
            border-bottom: none;
        }

        .modal-header .btn-close {
            filter: brightness(0) invert(1);
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            padding: 12px 16px;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }

        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }

        .scroll-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .scroll-to-top:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        /* Pagination Styles */
        .pagination {
            justify-content: center;
            margin: 3rem 0;
        }

        .pagination .page-link {
            border: none;
            color: var(--primary-color);
            font-weight: 500;
            padding: 0.75rem 1rem;
            margin: 0 0.25rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .pagination .page-item.active .page-link {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background: #f8f9fa;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .main-content {
                margin-top: -20px;
            }

            .filter-card .card-body {
                padding: 1.5rem;
            }

            .scroll-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="/">
                <i class="fas fa-boxes"></i> InventoryPro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-bold text-primary" href="/public/goods/">Browse Goods</a>
                    </li>
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/private-seller-store/">My Store</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile/"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="/manage-inventory/"><i class="fas fa-boxes me-2"></i>Inventory</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'logout' %}" style="display: inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item border-0 bg-transparent w-100 text-start">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/login/">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-primary px-3 ms-2" href="/register/">Sign Up</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Banner -->
    <section class="hero-banner">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Browse All Products</h1>
                <p class="hero-subtitle">Discover amazing products from verified sellers</p>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <!-- Filters Section -->
            <div class="filter-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Search & Filter</h5>
                        <button class="btn btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    
                    <div class="collapse show" id="filterCollapse">
                        <form method="get" id="filterForm">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="nameFilter" class="form-label">Search Products</label>
                                    <input type="text" class="form-control" id="nameFilter" name="q"
                                           value="{{ filters.search_query }}" placeholder="Search products, stores...">
                                </div>
                                <div class="col-md-2">
                                    <label for="minPriceFilter" class="form-label">Min Price</label>
                                    <input type="number" class="form-control" id="minPriceFilter" name="min_price"
                                           value="{{ filters.min_price }}" step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="col-md-2">
                                    <label for="maxPriceFilter" class="form-label">Max Price</label>
                                    <input type="number" class="form-control" id="maxPriceFilter" name="max_price"
                                           value="{{ filters.max_price }}" step="0.01" min="0" placeholder="999.99">
                                </div>
                                <div class="col-md-2">
                                    <label for="categoryFilter" class="form-label">Category</label>
                                    <select class="form-control" id="categoryFilter" name="category">
                                        <option value="">All Categories</option>
                                        {% for category in categories %}
                                            <option value="{{ category.name }}" {% if filters.category == category.name %}selected{% endif %}>
                                                {{ category.name }}
                                            </option>
                                        {% empty %}
                                            <option value="">No categories available</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="countryFilter" class="form-label">Country</label>
                                    <select class="form-control" id="countryFilter" name="country">
                                        <option value="">All Countries</option>
                                        {% for country in countries %}
                                            <option value="{{ country.id }}" {% if filters.country_id == country.id|stringformat:"s" %}selected{% endif %}>
                                                {{ country.name }}
                                            </option>
                                        {% empty %}
                                            <option value="">No countries available</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="row g-3 mt-2">
                                <div class="col-md-2">
                                    <label for="starRatingFilter" class="form-label">Star Rating</label>
                                    <select class="form-control" id="starRatingFilter" name="star_rating">
                                        <option value="">All Ratings</option>
                                        {% for rating_value, rating_display in star_ratings %}
                                            <option value="{{ rating_value }}" {% if filters.star_rating == rating_value|stringformat:"s" %}selected{% endif %}>
                                                {{ rating_display }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="conditionFilter" class="form-label">Condition</label>
                                    <select class="form-control" id="conditionFilter" name="is_used">
                                        <option value="">All Conditions</option>
                                        <option value="false" {% if filters.is_used == "false" %}selected{% endif %}>New</option>
                                        <option value="true" {% if filters.is_used == "true" %}selected{% endif %}>Used</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="deliveryFilter" class="form-label">Delivery</label>
                                    <select class="form-control" id="deliveryFilter" name="available_for_delivery">
                                        <option value="">All Items</option>
                                        <option value="true" {% if filters.available_for_delivery == "true" %}selected{% endif %}>Available for Delivery</option>
                                        <option value="false" {% if filters.available_for_delivery == "false" %}selected{% endif %}>Pickup Only</option>
                                    </select>
                                </div>

                                <div class="col-md-6 d-flex align-items-end">
                                    <div class="btn-group w-100">
                                        <button type="submit" class="btn filter-btn flex-fill">
                                            <i class="fas fa-search me-2"></i>Search & Filter
                                        </button>
                                        <a href="{% url 'public_goods_list' %}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

<!-- Products Grid -->
{% if goods %}
    <div class="row g-4 mb-4">
        {% for item in goods %}
        <div class="col-lg-4 col-md-6">    
            <div class="product-card">
 <!-- Image Display - CORRECTED VERSION -->
{% if item.image_url %}
    <img src="/media/{{ item.image_url }}" 
         class="product-image w-100" 
         alt="{{ item.name }}"
         onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
{% elif item.images and item.images.0 %}
    <!-- If using images array, show first image -->
    <img src="/media/{{ item.images.0 }}" 
         class="product-image w-100" 
         alt="{{ item.name }}"
         onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
{% else %}
    <!-- No Image Placeholder -->
    <div class="product-placeholder">
        <i class="fas fa-image fa-3x text-muted"></i>
        <p class="text-muted mt-2">No image available</p>
    </div>
{% endif %}
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-0">{{ item.name }}</h5>
                        {% if item.contact_store %}
                            <span class="store-badge">
                                <i class="fas fa-store"></i>
                            </span>
                        {% endif %}
                    </div>
                    
                    <p class="card-text text-muted mb-3">
                        {{ item.description|default:"No description available"|truncatewords:15 }}
                    </p>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="price-tag">{{ item.currency_symbol }}{{ item.price|floatformat:2 }}</span>
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> {{ item.created_at|date:"M d, Y" }}
                        </small>
                    </div>
                    
                    {% if item.contact_store %}
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-store me-1"></i>{{ item.contact_store.store_name }}
                        </small>
                    </div>
                    {% endif %}
                </div>
                
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <a href="{% url 'public_goods_detail' item.id %}" class="btn btn-view-details">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a>
                        {% if item.contact_store %}
                        <button class="btn btn-outline-success btn-contact contact-btn"
                                data-bs-toggle="modal" 
                                data-bs-target="#contactModal"
                                data-store-name="{{ item.contact_store.store_name }}"
                                data-store-description="{{ item.contact_store.store_description }}"
                                data-email="{{ item.contact_store.email }}"
                                data-phone="{{ item.contact_store.phone_number }}"
                                data-goods-name="{{ item.name }}">
                            <i class="fas fa-phone me-2"></i>Contact Seller
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="no-results">
        <i class="fas fa-search-minus"></i>
        <h4 class="text-muted mb-3">No products found</h4>
        <p class="text-muted mb-4">Try adjusting your search filters or browse all available products</p>
        <a href="{% url 'public_goods_list' %}" class="btn btn-view-details">
            <i class="fas fa-refresh me-2"></i>Show All Products
        </a>
    </div>

{% endif %}

    <!-- Pagination -->
    {% if page_obj %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="Products pagination">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>

            <div class="text-center text-muted">
                <small>
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} products
                </small>
            </div>
        </div>
    </div>
    {% endif %}

 </div>
    </div>

    <!-- Scroll to Top Button -->
    <button id="scrollToTop" class="scroll-to-top" title="Scroll to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Contact Modal -->
    <div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contactModalLabel">
                        <i class="fas fa-phone me-2"></i>Contact Seller
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="text-center mb-4">
                        <h6 class="mb-1">Interested in:</h6>
                        <span id="contactGoodsName" class="h5 text-primary"></span>
                    </div>
                    
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="card bg-light border-0">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-store me-2 text-primary"></i>
                                        <span id="contactStoreName"></span>
                                    </h6>
                                    <p class="card-text small text-muted" id="contactStoreDescription"></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-envelope me-3 text-primary"></i>
                                <div>
                                    <strong>Email:</strong><br>
                                    <a href="#" id="contactEmail" class="text-decoration-none"></a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12" id="contactPhoneDiv" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-phone me-3 text-primary"></i>
                                <div>
                                    <strong>Phone:</strong><br>
                                    <a href="#" id="contactPhone" class="text-decoration-none"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Close
                    </button>
                    <button type="button" class="btn btn-view-details" id="sendEmailBtn">
                        <i class="fas fa-envelope me-2"></i>Send Email
                    </button>
                </div>
            </div>
        </div>
    </div>

<!-- Footer -->
<footer class="bg-dark text-light py-5">
    <div class="container">
        <div class="row">
            <!-- Company Info Section -->
            <div class="col-lg-4 mb-4">
                <h5 class="fw-bold mb-3 text-white">Company House</h5>
                <p class="text-light mb-3">
                    We are a registered IT company in the UK with company number <span class="text-warning">14676167</span>
                </p>
            </div>

            <!-- Contact Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Contact</h6>
                <div class="contact-info">
                    <p class="text-light mb-2">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <strong>Address:</strong> 13L Queensway, Ponders End, Enfield, London EN3 4SA
                    </p>
                    <p class="text-light mb-2">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <strong>Email:</strong> <EMAIL>
                    </p>
                    <p class="text-light mb-0">
                        <i class="fas fa-phone me-2 text-primary"></i>
                        <strong>Tel:</strong> 07500503952
                    </p>
                </div>
            </div>

            <!-- Subscribe Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Subscribe Now</h6>
                <p class="text-light mb-3">
                    Don't miss our future updates! Get Subscribed Today!
                </p>
                <div class="mb-3">
            <div class="col-md-6 text-md-start">
                <a href="/register/" class="btn btn-primary">
                    <i class="fas fa-rocket me-2"></i>Register
                </a>
            </div>
                </div>
                <!-- Social Icons -->
                <div class="social-icons">
                    <a href="https://www.facebook.com/amatip.info.tech" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://x.com/amatipIT" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.tiktok.com/@amatip_it?is_from_webapp=1&sender_device=pc" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-tiktok"></i>
                    </a>
                    <a href="https://youtube.com/@amatip_it?si=eQ54UaVIM-DgLOAr" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="https://www.instagram.com/amatip_it/profilecard/?igsh=MWRzbGV5b3h1MTQ2Yw==" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="d-flex flex-wrap gap-4">
                    <a href="#features" class="text-muted text-decoration-none">Features</a>
                    <a href="/public/goods/" class="text-muted text-decoration-none">Browse Goods</a>
                    <a href="/login/" class="text-muted text-decoration-none">Login</a>
                    <a href="/register/" class="text-muted text-decoration-none">Sign Up</a>
                </div>
            </div>

        </div>

        <hr class="my-4 border-secondary">
        
        <!-- Copyright -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-muted">&copy; 2023 Amatip. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <small class="text-muted">Built with Django & Bootstrap</small>
            </div>
        </div>
    </div>
</footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Contact button click handlers
            document.querySelectorAll('.contact-btn').forEach(button => {
                button.addEventListener('click', function() {
                    document.getElementById('contactGoodsName').textContent = this.dataset.goodsName;
                    document.getElementById('contactStoreName').textContent = this.dataset.storeName;
                    document.getElementById('contactStoreDescription').textContent = this.dataset.storeDescription || 'No description available';
                    
                    const email = this.dataset.email;
                    const phone = this.dataset.phone;
                    
                    const emailLink = document.getElementById('contactEmail');
                    emailLink.textContent = email;
                    emailLink.href = `mailto:${email}?subject=Inquiry about ${this.dataset.goodsName}`;
                    
                    const phoneDiv = document.getElementById('contactPhoneDiv');
                    if (phone && phone !== 'None' && phone !== '') {
                        const phoneLink = document.getElementById('contactPhone');
                        phoneLink.textContent = phone;
                        phoneLink.href = `tel:${phone}`;
                        phoneDiv.style.display = 'block';
                    } else {
                        phoneDiv.style.display = 'none';
                    }
                    
                    // Update send email button
                    document.getElementById('sendEmailBtn').onclick = function() {
                        window.location.href = `mailto:${email}?subject=Inquiry about ${button.dataset.goodsName}&body=Hi, I'm interested in your ${button.dataset.goodsName}. Could you please provide more details?`;
                    };
                });
            });
            
            // Smooth navbar background transition
            window.addEventListener('scroll', () => {
                const navbar = document.querySelector('.navbar');
                if (window.scrollY > 50) {
                    navbar.style.background = 'rgba(255,255,255,0.98)';
                } else {
                    navbar.style.background = 'rgba(255,255,255,0.95)';
                }
            });

            // Form enhancements
            const form = document.getElementById('filterForm');
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
                submitBtn.disabled = true;

                // Re-enable after 3 seconds if still on page
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 3000);
            });

            // Dynamic state loading based on country selection
            const countrySelect = document.getElementById('countryFilter');
            if (countrySelect) {
                countrySelect.addEventListener('change', function() {
                    const countryId = this.value;
                    // For now, we'll just submit the form when country changes
                    // In a more advanced implementation, we could load states via AJAX
                    if (countryId) {
                        console.log('Country selected:', countryId);
                        // You can add AJAX call here to load states for the selected country
                    }
                });
            }

            // Scroll to top functionality
            const scrollToTopBtn = document.getElementById('scrollToTop');

            // Show/hide scroll to top button
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    scrollToTopBtn.classList.add('show');
                } else {
                    scrollToTopBtn.classList.remove('show');
                }
            });

            // Scroll to top when button is clicked
            scrollToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>