<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Fancy Receipt</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap');
    body {
      font-family: 'Georgia', serif;
      max-width: 600px;
      margin: 40px auto;
      padding: 20px;
      background: #fdf6f0;
      border: 2px solid #e67e22;
      border-radius: 15px;
      color: #7f4f24;
    }
    h1 {
      font-family: 'Great Vibes', cursive;
      text-align: center;
      font-size: 3em;
      margin-bottom: 10px;
      color: #d35400;
    }
    .company {
      text-align: center;
      font-size: 1.1em;
      margin-bottom: 20px;
      font-weight: 700;
      letter-spacing: 2px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 25px;
    }
    th, td {
      padding: 10px 12px;
      border-bottom: 1px solid #e67e22;
      text-align: left;
    }
    th {
      background-color: #f9d5b3;
      color: #7f4f24;
      font-weight: 700;
    }
    .total {
      font-size: 1.5em;
      font-weight: 700;
      text-align: right;
      margin-top: 15px;
      color: #d35400;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      font-style: italic;
      color: #a67c48;
    }
    .signature, .stamp {
      display: inline-block;
      width: 40%;
      margin: 10px 5%;
      padding-top: 10px;
      border-top: 1px solid #d35400;
      font-weight: 600;
      color: #a67c48;
      text-align: center;
    }
  </style>
</head>
<body>
  <h1>Receipt</h1>
  <div class="company-info">
    <p>{{ company_name }}</p>
    <p>Thank you for your purchase!</p>
  </div>
  <table>
  <thead>
    <tr>
      <th>Item</th>
      <th>Qty</th>
      <th>Unit Price</th>
      <th>Price</th>
    </tr>
  </thead>
  <tbody>
    {% for item in items %}
    <tr>
      <td>{{ item.name }}</td>
      <td>{{ item.quantity }}</td>
      <td>{{ item.unit_price }}</td>
      <td>{{ item.total_price }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>

  <p class="total">Total: {{ total_price }}</p>
  <div class="footer">
    <p>Signature:</p>
    {% if signature_url %}
      <img src="{{ signature_url }}" alt="Signature" />
    {% else %}
      <p>____________________</p>
    {% endif %}

    <p>Stamp:</p>
    {% if stamp_url %}
      <img src="{{ stamp_url }}" alt="Stamp" />
    {% else %}
      <p>____________________</p>
    {% endif %}
  </div>
</body>
</html>
