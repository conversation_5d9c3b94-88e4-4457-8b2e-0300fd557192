{% extends 'base.html' %}

{% block title %}{{ seller.first_name }} {{ seller.last_name }}'s Store{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Store Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-store me-2"></i>{{ seller.first_name }} {{ seller.last_name }}'s Store</h2>
            <p class="text-muted mb-0">@{{ seller.username }} • {{ total_goods }} Product{{ total_goods|pluralize }} • {{ total_stores }} Store{{ total_stores|pluralize }} • {{ total_properties_for_sale|default:0 }} Properties for Sale</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="shareStore()">
                <i class="fas fa-share-alt me-1"></i>Share Store
            </button>
            <button class="btn btn-primary" onclick="contactSeller()">
                <i class="fas fa-envelope me-1"></i>Contact Seller
            </button>
        </div>
    </div>

    <!-- Enhanced Marketplace-Style Filters -->
    <div class="container">
        <div class="row">
            <!-- Filter Sidebar -->
            <div class="col-lg-3">
                <div class="filter-sidebar">
                    <form method="GET" id="filterForm">
                        <!-- Search -->
                        <div class="filter-section">
                            <div class="filter-title">🔍 Search</div>
                            <input type="text" class="form-control" name="q"
                                   value="{{ filters.search_query }}"
                                   placeholder="Search products...">
                        </div>

                        <!-- Categories -->
                        <div class="filter-section">
                            <div class="filter-title">📂 Categories</div>
                            <select name="category" class="form-select">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if filters.category == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Price Range -->
                        <div class="filter-section">
                            <div class="filter-title">💰 Price Range</div>
                            <div class="price-range-inputs">
                                <input type="number" class="form-control" name="min_price"
                                       value="{{ filters.min_price }}" placeholder="Min" step="0.01">
                                <span class="text-muted">to</span>
                                <input type="number" class="form-control" name="max_price"
                                       value="{{ filters.max_price }}" placeholder="Max" step="0.01">
                            </div>
                        </div>

                        <!-- Condition -->
                        <div class="filter-section">
                            <div class="filter-title">🏷️ Condition</div>
                            <div class="filter-checkbox">
                                <input type="radio" name="condition" value="" id="condition_all"
                                       {% if not filters.condition %}checked{% endif %}>
                                <label for="condition_all">All Conditions</label>
                            </div>
                            <div class="filter-checkbox">
                                <input type="radio" name="condition" value="new" id="condition_new"
                                       {% if filters.condition == 'new' %}checked{% endif %}>
                                <label for="condition_new">New</label>
                            </div>
                            <div class="filter-checkbox">
                                <input type="radio" name="condition" value="used" id="condition_used"
                                       {% if filters.condition == 'used' %}checked{% endif %}>
                                <label for="condition_used">Used</label>
                            </div>
                        </div>

                        <!-- Availability -->
                        <div class="filter-section">
                            <div class="filter-title">📦 Availability</div>
                            <div class="filter-checkbox">
                                <input type="checkbox" name="available_for_delivery" value="true" id="delivery"
                                       {% if filters.available_for_delivery %}checked{% endif %}>
                                <label for="delivery">Available for Delivery</label>
                            </div>
                            <div class="filter-checkbox">
                                <input type="checkbox" name="available_for_bulk_sales" value="true" id="bulk"
                                       {% if filters.available_for_bulk_sales %}checked{% endif %}>
                                <label for="bulk">Bulk Sales Available</label>
                            </div>
                            <div class="filter-checkbox">
                                <input type="checkbox" name="in_stock_only" value="true" id="in_stock"
                                       {% if filters.in_stock_only %}checked{% endif %}>
                                <label for="in_stock">In Stock Only</label>
                            </div>
                        </div>



                        <!-- Stores -->
                        {% if stores.count > 1 %}
                        <div class="filter-section">
                            <div class="filter-title">🏪 Stores</div>
                            <select name="store" class="form-select">
                                <option value="">All Stores</option>
                                {% for store in stores %}
                                    <option value="{{ store.id }}" {% if filters.store == store.id|stringformat:"s" %}selected{% endif %}>
                                        {{ store.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <!-- Sort -->
                        <div class="filter-section">
                            <div class="filter-title">🔄 Sort By</div>
                            <select name="sort" class="form-select">
                                <option value="">Default</option>
                                <option value="price_low" {% if filters.sort == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                                <option value="price_high" {% if filters.sort == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                                <option value="name" {% if filters.sort == 'name' %}selected{% endif %}>Name A-Z</option>
                                <option value="newest" {% if filters.sort == 'newest' %}selected{% endif %}>Newest First</option>
                            </select>
                        </div>

                        <!-- Filter Actions -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i>Apply Filters
                            </button>
                            <a href="{% url 'private_seller_store_public' seller.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear All
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-lg-9">

                <!-- Properties for Sale Section -->
                {% if properties_for_sale %}
                <div class="mb-5">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="mb-0">
                            <i class="fas fa-home me-2 text-success"></i>
                            Properties for Sale
                        </h4>
                        <a href="{% url 'properties_for_sale' %}" class="btn btn-outline-success btn-sm">
                            View All Properties
                        </a>
                    </div>

                    <div class="row g-3">
                        {% for property in properties_for_sale %}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 border-success">
                                <div class="position-relative">
                                    {% if property.images and property.images.0 %}
                                        <img src="/media/{{ property.images.0 }}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="{{ property.name }}" onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                                    {% else %}
                                        <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 150px;">
                                            <i class="fas fa-home fa-3x text-muted"></i>
                                        </div>
                                    {% endif %}

                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success">
                                            ${{ property.sale_price|floatformat:0 }}
                                        </span>
                                    </div>

                                    {% if property.is_negotiable %}
                                        <div class="position-absolute top-0 start-0 m-2">
                                            <span class="badge bg-warning text-dark">Negotiable</span>
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="card-body">
                                    <h6 class="card-title">{{ property.name }}</h6>
                                    <p class="card-text text-muted small">
                                        {{ property.sale_description|default:property.description|truncatewords:10 }}
                                    </p>

                                    {% if property.location %}
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ property.location }}
                                    </p>
                                    {% endif %}

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">{{ property.created_at|date:"M d, Y" }}</small>
                                        <a href="{% url 'property_sale_detail' property.id %}" class="btn btn-success btn-sm">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

    <!-- Products Grid -->
    <div class="container">
        {% if goods %}
            <div class="row g-4">
                {% for item in goods %}
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="product-card h-100">
                        <div class="product-image-container">
                            {% if item.first_image_url %}
                                <img src="{{ item.first_image_url }}" class="product-image" alt="{{ item.name }}">
                            {% else %}
                                <div class="product-placeholder">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                            {% if item.is_used %}
                                <span class="badge bg-warning position-absolute top-0 end-0 m-2">Used</span>
                            {% else %}
                                <span class="badge bg-success position-absolute top-0 end-0 m-2">New</span>
                            {% endif %}
                        </div>
                        
                        <div class="product-info p-3">
                            <h6 class="product-title mb-2">{{ item.name }}</h6>
                            <p class="product-description text-muted small mb-2">
                                {{ item.description|truncatewords:15|default:"No description available" }}
                            </p>
                            
                            <div class="product-meta mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-store me-1"></i>{{ item.store.name }}
                                </small>
                                {% if item.category.name %}
                                <br><small class="text-muted">
                                    <i class="fas fa-tag me-1"></i>{{ item.category.name }}
                                </small>
                                {% endif %}
                            </div>

                            {% if item.star_rating %}
                            <div class="product-rating mb-2">
                                <small class="text-warning">{{ item.star_rating_display }}</small>
                            </div>
                            {% endif %}

                            <div class="product-price mb-3">
                                <span class="price-current">{{ item.currency_symbol }}{{ item.price|floatformat:2 }}</span>
                                {% if item.available_for_delivery %}
                                    <small class="text-success d-block">
                                        <i class="fas fa-truck me-1"></i>Delivery Available
                                    </small>
                                {% endif %}
                            </div>

                            <div class="product-actions d-grid gap-2">
                                <button class="btn btn-primary btn-sm" onclick="inquireAboutProduct('{{ item.id }}', '{{ item.name|escapejs }}', '{{ seller.username|escapejs }}')">
                                    <i class="fas fa-envelope me-1"></i>Inquire Now
                                </button>
                                <div class="d-flex gap-1">
                                    <button class="btn btn-outline-info btn-sm flex-fill" onclick="viewProductDetails('{{ item.id }}')">
                                        <i class="fas fa-eye me-1"></i>Details
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm flex-fill" onclick="shareProduct('{{ request.build_absolute_uri }}', '{{ item.name|escapejs }}')">
                                        <i class="fas fa-share-alt me-1"></i>Share
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if goods.has_other_pages %}
            <nav aria-label="Products pagination" class="mt-5">
                <ul class="pagination justify-content-center">
                    {% if goods.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ goods.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">Page {{ goods.number }} of {{ goods.paginator.num_pages }}</span>
                    </li>

                    {% if goods.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ goods.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ goods.paginator.num_pages }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
                <h4>No Products Found</h4>
                <p class="text-muted">
                    {% if filters.search_query or filters.category or filters.min_price or filters.max_price %}
                        Try adjusting your filters to see more products.
                    {% else %}
                        This seller hasn't added any products yet.
                    {% endif %}
                </p>
                {% if filters.search_query or filters.category or filters.min_price or filters.max_price %}
                <a href="{% url 'private_seller_store_public' seller.id %}" class="btn btn-outline-primary">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<style>
.store-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin: -1.5rem -15px 0;
}

.store-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255,255,255,0.3);
}

.store-avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid rgba(255,255,255,0.3);
}

.store-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.store-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.company-name {
    font-size: 1rem;
    opacity: 0.8;
}

.product-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.2s ease;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image-container {
    position: relative;
    height: 160px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-placeholder {
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.price-current {
    font-size: 1.1rem;
    font-weight: 700;
    color: #27ae60;
}

.badge {
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .store-header {
        padding: 2rem 0;
    }
    
    .store-title {
        font-size: 1.5rem;
    }
    
    .store-actions {
        margin-top: 1rem;
    }
}

/* Enhanced Marketplace Filter Styles */
.filter-sidebar {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 20px;
    height: fit-content;
}

.filter-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.filter-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-range-inputs {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 0.5rem;
    align-items: center;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.filter-checkbox input[type="checkbox"],
.filter-checkbox input[type="radio"] {
    margin: 0;
}

.filter-checkbox label {
    margin: 0;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
}

.results-header {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<script>
function shareStore() {
    const storeURL = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: '{{ seller.first_name }} {{ seller.last_name }}\'s Store',
            text: 'Check out this amazing store!',
            url: storeURL
        }).catch(console.error);
    } else {
        navigator.clipboard.writeText(storeURL).then(() => {
            showAlert('success', 'Store link copied to clipboard!');
        }).catch(() => {
            showAlert('info', `Store link: ${storeURL}`);
        });
    }
}

function contactSeller() {
    showAlert('info', 'Contact functionality will be implemented soon!');
}

// Enhanced inquire functionality from marketplace
function inquireAboutProduct(productId, productName, sellerUsername) {
    // Create inquiry modal
    const modalHTML = `
        <div class="modal fade" id="inquiryModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Inquire About: ${productName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="inquiryForm">
                            <div class="mb-3">
                                <label for="inquirerName" class="form-label">Your Name</label>
                                <input type="text" class="form-control" id="inquirerName" required>
                            </div>
                            <div class="mb-3">
                                <label for="inquirerEmail" class="form-label">Your Email</label>
                                <input type="email" class="form-control" id="inquirerEmail" required>
                            </div>
                            <div class="mb-3">
                                <label for="inquirerPhone" class="form-label">Your Phone (Optional)</label>
                                <input type="tel" class="form-control" id="inquirerPhone">
                            </div>
                            <div class="mb-3">
                                <label for="inquiryMessage" class="form-label">Message</label>
                                <textarea class="form-control" id="inquiryMessage" rows="4"
                                          placeholder="I'm interested in this product. Please provide more details...">${'I am interested in "' + productName + '". Could you please provide more details about availability, pricing, and delivery options?'}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitInquiry('${productId}', '${productName}', '${sellerUsername}')">
                            <i class="fas fa-paper-plane me-1"></i>Send Inquiry
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('inquiryModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('inquiryModal'));
    modal.show();
}

function submitInquiry(productId, productName, sellerUsername) {
    const form = document.getElementById('inquiryForm');
    const formData = new FormData(form);

    const inquiryData = {
        product_id: productId,
        product_name: productName,
        seller_username: sellerUsername,
        inquirer_name: document.getElementById('inquirerName').value,
        inquirer_email: document.getElementById('inquirerEmail').value,
        inquirer_phone: document.getElementById('inquirerPhone').value,
        message: document.getElementById('inquiryMessage').value
    };

    // Validate required fields
    if (!inquiryData.inquirer_name || !inquiryData.inquirer_email || !inquiryData.message) {
        showAlert('danger', 'Please fill in all required fields.');
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#inquiryModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';
    submitBtn.disabled = true;

    // Submit inquiry (you can implement actual backend submission here)
    setTimeout(() => {
        // Simulate successful submission
        showAlert('success', `Your inquiry about "${productName}" has been sent to ${sellerUsername}!`);

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('inquiryModal'));
        modal.hide();

        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // You can implement actual API call here
        console.log('Inquiry submitted:', inquiryData);
    }, 1500);
}

function shareProduct(productId, productName) {
    const productURL = `${window.location.origin}/goods/${productId}/detail/`;
    showShareModal(productURL, productName, 'product');
}

function shareStore() {
    const storeURL = window.location.href;
    const storeName = `{{ seller.first_name }} {{ seller.last_name }}'s Store`;
    showShareModal(storeURL, storeName, 'store');
}

function showShareModal(url, name, type) {
    const modalHTML = `
        <div class="modal fade" id="shareModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Share ${type === 'product' ? 'Product' : 'Store'}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <h6>${name}</h6>
                            <p class="text-muted small">${url}</p>
                        </div>

                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">Share URL</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="shareUrl" value="${url}" readonly>
                                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('shareUrl')">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                </div>
                            </div>

                            <div class="col-12">
                                <label class="form-label">Share via Email</label>
                                <div class="d-grid">
                                    <button class="btn btn-primary" onclick="shareViaEmail('${url}', '${name}', '${type}')">
                                        <i class="fas fa-envelope me-1"></i>Open Email Client
                                    </button>
                                </div>
                            </div>

                            <div class="col-12">
                                <label class="form-label">Social Media</label>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-primary btn-sm" onclick="shareOnFacebook('${url}', '${name}')">
                                        <i class="fab fa-facebook-f"></i> Facebook
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="shareOnTwitter('${url}', '${name}')">
                                        <i class="fab fa-twitter"></i> Twitter
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="shareOnWhatsApp('${url}', '${name}')">
                                        <i class="fab fa-whatsapp"></i> WhatsApp
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('shareModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showAlert('success', 'URL copied to clipboard!');
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(element.value).then(() => {
            showAlert('success', 'URL copied to clipboard!');
        }).catch(() => {
            showAlert('danger', 'Failed to copy URL. Please copy manually.');
        });
    }
}

function shareViaEmail(url, name, type) {
    const subject = encodeURIComponent(`Check out this ${type}: ${name}`);
    const body = encodeURIComponent(`Hi,

I wanted to share this ${type} with you: ${name}

You can view it here: ${url}

Best regards`);

    const emailUrl = `mailto:?subject=${subject}&body=${body}`;
    window.open(emailUrl);
}

function shareOnFacebook(url, name) {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(name)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
}

function shareOnTwitter(url, name) {
    const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(`Check out: ${name}`)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, name) {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(`Check out: ${name} - ${url}`)}`;
    window.open(whatsappUrl, '_blank');
}

function contactSeller() {
    const sellerEmail = '{{ seller.email|default:"" }}';
    const sellerName = '{{ seller.first_name }} {{ seller.last_name }}';

    if (sellerEmail) {
        const subject = encodeURIComponent(`Inquiry about your store`);
        const body = encodeURIComponent(`Hi ${sellerName},

I'm interested in your products and would like to know more about your store.

Best regards`);

        const emailUrl = `mailto:${sellerEmail}?subject=${subject}&body=${body}`;
        window.open(emailUrl);
    } else {
        showAlert('info', 'Contact information not available for this seller.');
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Enhanced filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        // Auto-submit on filter changes
        const autoSubmitElements = filterForm.querySelectorAll('select, input[type="checkbox"], input[type="radio"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', () => {
                setTimeout(() => filterForm.submit(), 300);
            });
        });

        // Submit on Enter for text inputs
        const textInputs = filterForm.querySelectorAll('input[type="text"], input[type="number"]');
        textInputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    filterForm.submit();
                }
            });
        });
    }
});

function viewProductDetails(productId) {
    // Redirect to public product detail page (no login required)
    window.location.href = `/public/goods/detail/${productId}/`;
}
</script>
{% endblock %}
