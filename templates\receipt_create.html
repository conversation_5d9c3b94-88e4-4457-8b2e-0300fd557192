{% extends "base.html" %}

{% block title %}Create Receipt - Inventory Management{% endblock %}

{% block content %}
<div class="container my-5">
    <h3 class="text-center mb-4">Create New Receipt</h3>

    {% if message %}
        <div class="alert alert-success">
            {{ message }} (Receipt ID: {{ receipt_id }})
        </div>
    {% elif error %}
        <div class="alert alert-danger">
            {{ error }}
        </div>
    {% endif %}

    <form id="createReceiptForm" enctype="multipart/form-data" method="POST">
        {% csrf_token %}
        
        <!-- Template ID -->
        <div class="mb-3">
            <label for="template_id" class="form-label">Template</label>
            <select class="form-select" id="template_id" name="template_id" required>
                <option value="">Select Template</option>
                {% for template in templates %}
                    <option value="{{ template.id }}">{{ template.name }}</option>
                {% endfor %}
            </select>
        </div>

        <!-- Client Information Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Client Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="client_name" class="form-label">Client Name</label>
                            <input type="text" class="form-control" id="client_name" name="client_name" placeholder="Enter client name" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="client_company" class="form-label">Client Company</label>
                            <input type="text" class="form-control" id="client_company" name="client_company" placeholder="Enter client company" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="client_email" class="form-label">Client Email</label>
                            <input type="email" class="form-control" id="client_email" name="client_email" placeholder="Enter client email" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="client_phone" class="form-label">Client Phone</label>
                            <input type="tel" class="form-control" id="client_phone" name="client_phone" placeholder="Enter client phone" />
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="client_address" class="form-label">Client Address</label>
                    <textarea class="form-control" id="client_address" name="client_address" rows="3" placeholder="Enter client address"></textarea>
                </div>
            </div>
        </div>
        

        <!-- Currency Selection -->
        <div class="mb-3">
            <label for="currency" class="form-label">Currency</label>
            <select id="currency" name="currency" class="form-select" required>
                <option value="USD" selected>USD - US Dollar ($)</option>
                <option value="EUR">EUR - Euro (€)</option>
                <option value="NGN">NGN - Nigerian Naira (₦)</option>
                <option value="GBP">GBP - British Pound (£)</option>
                <!-- Add more currencies as needed -->
            </select>
        </div>

        <!-- Items Section -->
        <div class="mb-4">
            <h5>Items</h5>
            <div id="items-container">
                <div class="item-row mb-3" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Item Name</label>
                            <input type="text" class="form-control" name="item_name[]" required />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Quantity</label>
                            <input type="number" class="form-control item-quantity" name="item_quantity[]" min="1" value="1" required />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Unit Price</label>
                            <input type="number" class="form-control item-price" name="item_unit_price[]" step="0.01" min="0" required />
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Total</label>
                            <input type="text" class="form-control item-total" readonly />
                        </div>
                    </div>
                    <button type="button" class="btn btn-danger btn-sm mt-2 remove-item" style="display: none;">Remove Item</button>
                </div>
            </div>
            
            <button type="button" id="add-item-btn" class="btn btn-secondary mb-3">Add Another Item</button>

            <div class="row mb-3">
  <div class="col-md-6">
    <label for="tax_amount" class="form-label">Tax Amount (Optional)</label>
    <input type="number" step="0.01" min="0" class="form-control" id="tax_amount" name="tax_amount" placeholder="Enter tax amount" />
  </div>
  <div class="col-md-6">
    <label for="discount_amount" class="form-label">Discount Amount (Optional)</label>
    <input type="number" step="0.01" min="0" class="form-control" id="discount_amount" name="discount_amount" placeholder="Enter discount amount" />
  </div>
</div>

            
            <div class="row">
                <div class="col-md-8"></div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>Grand Total: <span id="currency-symbol">$</span><span id="grand-total">0.00</span></h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Signature -->
        <div class="mb-3">
            <label for="signature" class="form-label">Signature Image</label>
            <input type="file" class="form-control" id="signature" name="signature" />
        </div>

        <!-- Stamp -->
        <div class="mb-3">
            <label for="stamp" class="form-label">Official Stamp Image</label>
            <input type="file" class="form-control" id="stamp" name="stamp" />
        </div>

        <!-- Submit Button -->
        <button type="submit" class="btn btn-primary">Create Receipt</button>
        
    </form>
</div>

<script>
    let itemCount = 1;

    // Map currency codes to symbols
    const currencySymbols = {
        'USD': '$',
        'EUR': '€',
        'NGN': '₦',
        'GBP': '£'
    };

    const currencySelect = document.getElementById('currency');
    const currencySymbolSpan = document.getElementById('currency-symbol');

    function updateCurrencySymbol() {
        const selected = currencySelect.value;
        currencySymbolSpan.textContent = currencySymbols[selected] || selected;
    }

    currencySelect.addEventListener('change', () => {
        updateCurrencySymbol();
    });

    function updateItemTotal(row) {
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;
        const total = quantity * price;
        row.querySelector('.item-total').value = total.toFixed(2);
        updateGrandTotal();
    }

function updateGrandTotal() {
    let subtotal = 0;
    document.querySelectorAll('.item-total').forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });

    const tax = parseFloat(document.getElementById('tax_amount').value) || 0;
    const discount = parseFloat(document.getElementById('discount_amount').value) || 0;

    const grandTotal = subtotal + tax - discount;

    document.getElementById('grand-total').textContent = grandTotal.toFixed(2);
}

document.getElementById('tax_amount').addEventListener('input', updateGrandTotal);
document.getElementById('discount_amount').addEventListener('input', updateGrandTotal);

    function attachEventListeners(row) {
        row.querySelector('.item-quantity').addEventListener('input', () => updateItemTotal(row));
        row.querySelector('.item-price').addEventListener('input', () => updateItemTotal(row));
        
        const removeBtn = row.querySelector('.remove-item');
        removeBtn.addEventListener('click', function() {
            row.remove();
            updateGrandTotal();
            toggleRemoveButtons();
        });
    }

    function toggleRemoveButtons() {
        const removeButtons = document.querySelectorAll('.remove-item');
        removeButtons.forEach(btn => {
            btn.style.display = removeButtons.length > 1 ? 'inline-block' : 'none';
        });
    }

    // Add event listeners to the first row
    attachEventListeners(document.querySelector('.item-row'));

    document.getElementById('add-item-btn').addEventListener('click', function() {
        itemCount++;
        const container = document.getElementById('items-container');
        const newItemHTML = `
            <div class="item-row mb-3" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Item Name</label>
                        <input type="text" class="form-control" name="item_name[]" required />
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Quantity</label>
                        <input type="number" class="form-control item-quantity" name="item_quantity[]" min="1" value="1" required />
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Unit Price</label>
                        <input type="number" class="form-control item-price" name="item_unit_price[]" step="0.01" min="0" required />
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Total</label>
                        <input type="text" class="form-control item-total" readonly />
                    </div>
                </div>
                <button type="button" class="btn btn-danger btn-sm mt-2 remove-item">Remove Item</button>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', newItemHTML);
        
        // Attach event listeners to the new row
        const newRow = container.lastElementChild;
        attachEventListeners(newRow);
        toggleRemoveButtons();
    });

    // Initialize calculations and currency symbol for the first row
    updateGrandTotal();
    toggleRemoveButtons();
    updateCurrencySymbol();
</script>

{% endblock %}
