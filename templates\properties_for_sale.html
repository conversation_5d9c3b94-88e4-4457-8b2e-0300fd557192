{% extends 'base.html' %}

{% block title %}Properties for Sale{% endblock %}

{% block extra_css %}
<style>
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 2rem;
}

.property-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.property-image {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.property-image .no-image {
    color: #6c757d;
    font-size: 3rem;
}

.price-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--accent-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.negotiable-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.property-info {
    padding: 1.5rem;
}

.property-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.property-location {
    color: #6b7280;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.property-description {
    color: #4b5563;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.property-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.contact-info {
    font-size: 0.9rem;
    color: #6b7280;
}

.btn-view-property {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-view-property:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
}

.filters-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.pagination {
    justify-content: center;
    margin: 3rem 0;
}

.pagination .page-link {
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
}
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">Properties for Sale</h1>
                <p class="lead mb-0">Find your dream property from our curated listings</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="text-white">
                    <h3>{{ page_obj.paginator.count }}</h3>
                    <p class="mb-0">Properties Available</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Filters Section -->
    <div class="filters-section">
        <h5 class="mb-3">
            <i class="fas fa-filter me-2"></i>
            Filter Properties
        </h5>
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="q" 
                       value="{{ search_query }}" placeholder="Location, name, description...">
            </div>
            <div class="col-md-2">
                <label for="min_price" class="form-label">Min Price</label>
                <input type="number" class="form-control" id="min_price" name="min_price" 
                       value="{{ min_price }}" placeholder="0">
            </div>
            <div class="col-md-2">
                <label for="max_price" class="form-label">Max Price</label>
                <input type="number" class="form-control" id="max_price" name="max_price" 
                       value="{{ max_price }}" placeholder="Any">
            </div>
            <div class="col-md-3">
                <label for="property_type" class="form-label">Property Type</label>
                <select class="form-select" id="property_type" name="property_type">
                    <option value="">All Types</option>
                    {% for type_value, type_label in property_types %}
                        <option value="{{ type_value }}" {% if property_type == type_value %}selected{% endif %}>
                            {{ type_label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>Search
                </button>
            </div>
        </form>
    </div>

    <!-- Properties Grid -->
    <div class="row">
        {% for property in properties %}
        <div class="col-lg-4 col-md-6">
            <div class="property-card">
                <div class="property-image">
                    {% if property.first_image %}
                        <img src="{{ property.first_image }}" alt="{{ property.name }}">
                    {% else %}
                        <i class="fas fa-home no-image"></i>
                    {% endif %}
                    
                    <div class="price-badge">
                        ${{ property.sale_price|floatformat:0 }}
                    </div>
                    
                    {% if property.is_negotiable %}
                        <div class="negotiable-badge">Negotiable</div>
                    {% endif %}
                </div>
                
                <div class="property-info">
                    <h3 class="property-title">{{ property.name }}</h3>
                    
                    <div class="property-location">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        {{ property.location|default:"Location not specified" }}
                    </div>
                    
                    <p class="property-description">
                        {{ property.sale_description|default:property.description|truncatechars:120 }}
                    </p>
                    
                    <div class="property-meta">
                        <div class="contact-info">
                            {% if property.contact_phone %}
                                <div><i class="fas fa-phone me-1"></i>{{ property.contact_phone }}</div>
                            {% endif %}
                            {% if property.contact_email %}
                                <div><i class="fas fa-envelope me-1"></i>{{ property.contact_email }}</div>
                            {% endif %}
                        </div>
                        
                        <a href="{% url 'property_sale_detail' property.id %}" class="btn-view-property">
                            <i class="fas fa-eye me-1"></i>View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-home fa-4x text-muted mb-3"></i>
                <h3 class="text-muted">No Properties Found</h3>
                <p class="text-muted">Try adjusting your search criteria</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Properties pagination">
        <ul class="pagination">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if property_type %}&property_type={{ property_type }}{% endif %}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if property_type %}&property_type={{ property_type }}{% endif %}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&q={{ search_query }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if property_type %}&property_type={{ property_type }}{% endif %}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if property_type %}&property_type={{ property_type }}{% endif %}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if property_type %}&property_type={{ property_type }}{% endif %}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    
    <div class="text-center text-muted">
        <small>
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} properties
        </small>
    </div>
    {% endif %}
</div>

<!-- Scroll to Top Button -->
<button id="scrollToTop" class="scroll-to-top" title="Scroll to top">
    <i class="fas fa-chevron-up"></i>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Scroll to top functionality
    const scrollToTopBtn = document.getElementById('scrollToTop');
    
    // Show/hide scroll to top button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('show');
        } else {
            scrollToTopBtn.classList.remove('show');
        }
    });

    // Scroll to top when button is clicked
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});
</script>
{% endblock %}
