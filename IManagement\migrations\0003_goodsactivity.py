# Generated by Django 5.2.3 on 2025-07-23 22:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('IManagement', '0002_userprofile_country_userprofile_state'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GoodsActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('created', 'Product Created'), ('updated', 'Product Updated'), ('stock_added', 'Stock Added'), ('stock_reduced', 'Stock Reduced'), ('sale_recorded', 'Sale Recorded'), ('price_updated', 'Price Updated'), ('status_changed', 'Status Changed'), ('category_changed', 'Category Changed'), ('store_changed', 'Store Changed'), ('image_added', 'Image Added'), ('image_removed', 'Image Removed'), ('description_updated', 'Description Updated'), ('inventory_adjustment', 'Inventory Adjustment'), ('viewed', 'Product Viewed'), ('shared', 'Product Shared')], max_length=50)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('old_value', models.TextField(blank=True, null=True)),
                ('new_value', models.TextField(blank=True, null=True)),
                ('quantity_change', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('goods', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='IManagement.goods')),
            ],
            options={
                'verbose_name': 'Goods Activity',
                'verbose_name_plural': 'Goods Activities',
                'ordering': ['-created_at'],
            },
        ),
    ]
