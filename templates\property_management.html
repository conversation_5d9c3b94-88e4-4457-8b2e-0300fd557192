{% extends 'base.html' %}

{% block title %}Property Management - Inventory Management System{% endblock %}

{% block extra_css %}
<style>
    .property-card {
        border: none;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .property-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .property-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .property-type-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .condition-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .value-display {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1.25rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .stats-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-label {
        font-size: 0.8rem;
        opacity: 0.9;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }

    .property-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: auto;
    }

    .btn-sm {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        flex: 1;
    }

    .properties-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
    }

    @media (max-width: 768px) {
        .properties-grid {
            grid-template-columns: 1fr;
        }

        .stats-card {
            margin-bottom: 0.75rem;
        }
    }
    
    .modal-lg {
        max-width: 800px;
    }
    
    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .form-row .form-group {
        flex: 1;
    }
    
    .tags-input {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px;
        min-height: 38px;
    }
    
    .tag {
        display: inline-block;
        background: #007bff;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin: 2px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Property Management</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
            <i class="fas fa-plus"></i> Add Property
        </button>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-value">{{ total_properties }}</div>
                <div class="stats-label">Total Properties</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-value">₦{{ total_purchase_value|floatformat:0 }}</div>
                <div class="stats-label">Total Purchase Value</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-value">₦{{ total_current_value|floatformat:0 }}</div>
                <div class="stats-label">Current Value</div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="filter-section">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="Search properties...">
            </div>
            <div class="col-md-4">
                <label for="type" class="form-label">Property Type</label>
                <select class="form-select" id="type" name="type">
                    <option value="">All Types</option>
                    {% for value, label in property_types %}
                        <option value="{{ value }}" {% if selected_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                <a href="{% url 'property_management' %}" class="btn btn-outline-secondary">Clear</a>
            </div>
        </form>
    </div>
    
    <!-- Properties List -->
    <div class="properties-grid">
        {% for property in properties %}
        <div class="property-card">
                <div class="property-header">
                    <h5 class="mb-0">{{ property.name }}</h5>
                    <span class="property-type-badge bg-primary text-white">
                        {{ property.get_property_type_display }}
                    </span>
                </div>
                
                {% if property.description %}
                <p class="text-muted mb-3">{{ property.description|truncatewords:15 }}</p>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Condition</small>
                        <div class="condition-badge 
                            {% if property.condition == 'excellent' %}bg-success text-white
                            {% elif property.condition == 'very_good' %}bg-info text-white
                            {% elif property.condition == 'good' %}bg-primary text-white
                            {% elif property.condition == 'fair' %}bg-warning text-dark
                            {% else %}bg-danger text-white{% endif %}">
                            {{ property.get_condition_display }}
                        </div>
                    </div>
                    <div class="col-6">
                        {% if property.current_value %}
                        <small class="text-muted">Current Value</small>
                        <div class="value-display">{{ property.currency }} {{ property.current_value|floatformat:0 }}</div>
                        {% endif %}
                    </div>
                </div>
                
                {% if property.brand %}
                <div class="mb-2">
                    <small class="text-muted">Brand:</small> {{ property.brand }}
                </div>
                {% endif %}
                
                {% if property.get_tags_list %}
                <div class="mb-3">
                    {% for tag in property.get_tags_list %}
                        <span class="tag">{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <div class="property-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewProperty({{ property.id }})">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editProperty({{ property.id }})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteProperty({{ property.id }}, '{{ property.name }}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        {% empty %}
        <div class="text-center py-5">
            <i class="fas fa-home fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Properties Found</h4>
            <p class="text-muted">Start by adding your first property to track and manage your assets.</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                <i class="fas fa-plus"></i> Add Your First Property
            </button>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Add Property Modal -->
<div class="modal fade" id="addPropertyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Property</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPropertyForm">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name" class="form-label">Property Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="property_type" class="form-label">Type</label>
                            <select class="form-select" id="property_type" name="property_type">
                                {% for value, label in property_types %}
                                    <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="condition" class="form-label">Condition</label>
                            <select class="form-select" id="condition" name="condition">
                                <option value="excellent">Excellent</option>
                                <option value="very_good">Very Good</option>
                                <option value="good" selected>Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="currency" class="form-label">Currency</label>
                            <select class="form-select" id="currency" name="currency">
                                <option value="NGN">₦ Nigerian Naira</option>
                                <option value="USD">$ US Dollar</option>
                                <option value="EUR">€ Euro</option>
                                <option value="GBP">£ British Pound</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="purchase_price" class="form-label">Purchase Price</label>
                            <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="current_value" class="form-label">Current Value</label>
                            <input type="number" class="form-control" id="current_value" name="current_value" step="0.01">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date">
                        </div>
                        <div class="form-group">
                            <label for="warranty_expiry" class="form-label">Warranty Expiry</label>
                            <input type="date" class="form-control" id="warranty_expiry" name="warranty_expiry">
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="location" class="form-label">Location/Storage</label>
                        <input type="text" class="form-control" id="location" name="location" placeholder="Where is this property located?">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="brand" class="form-label">Brand</label>
                            <input type="text" class="form-control" id="brand" name="brand">
                        </div>
                        <div class="form-group">
                            <label for="model_number" class="form-label">Model Number</label>
                            <input type="text" class="form-control" id="model_number" name="model_number">
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="serial_number" class="form-label">Serial Number</label>
                        <input type="text" class="form-control" id="serial_number" name="serial_number">
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="insured" name="insured">
                        <label class="form-check-label" for="insured">
                            This property is insured
                        </label>
                    </div>
                    
                    <div id="insuranceFields" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="insurance_value" class="form-label">Insurance Value</label>
                                <input type="number" class="form-control" id="insurance_value" name="insurance_value" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="insurance_expiry" class="form-label">Insurance Expiry</label>
                                <input type="date" class="form-control" id="insurance_expiry" name="insurance_expiry">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="tags" class="form-label">Tags</label>
                        <input type="text" class="form-control" id="tags" name="tags" placeholder="Comma-separated tags">
                        <small class="form-text text-muted">e.g., electronics, warranty, expensive</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Property</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Property Modal -->
<div class="modal fade" id="editPropertyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Property</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPropertyForm">
                <div class="modal-body">
                    <input type="hidden" id="editPropertyId" name="property_id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editName" class="form-label">Property Name *</label>
                            <input type="text" class="form-control" id="editName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="editPropertyType" class="form-label">Type</label>
                            <select class="form-select" id="editPropertyType" name="property_type">
                                {% for value, label in property_types %}
                                    <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editCondition" class="form-label">Condition</label>
                            <select class="form-select" id="editCondition" name="condition">
                                {% for value, label in conditions %}
                                    <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editLocation" class="form-label">Location</label>
                            <input type="text" class="form-control" id="editLocation" name="location">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editPurchasePrice" class="form-label">Purchase Price</label>
                            <input type="number" class="form-control" id="editPurchasePrice" name="purchase_price" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="editCurrentValue" class="form-label">Current Value</label>
                            <input type="number" class="form-control" id="editCurrentValue" name="current_value" step="0.01">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="editNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Property</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Toggle insurance fields
document.getElementById('insured').addEventListener('change', function() {
    const insuranceFields = document.getElementById('insuranceFields');
    insuranceFields.style.display = this.checked ? 'block' : 'none';
});

// Add property form submission
document.getElementById('addPropertyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{% url "property_management" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to add property'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adding the property');
    });
});

// Edit property form submission
document.getElementById('editPropertyForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const propertyId = document.getElementById('editPropertyId').value;
    const formData = new FormData(this);

    // Convert FormData to JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        if (key !== 'property_id') {
            data[key] = value;
        }
    }

    fetch(`/properties/${propertyId}/`, {
        method: 'PUT',
        body: JSON.stringify(data),
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to update property'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the property');
    });
});

// Utility functions
function viewProperty(propertyId) {
    window.location.href = `/properties/${propertyId}/`;
}

function editProperty(propertyId) {
    // Fetch property data
    fetch(`/properties/${propertyId}/`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const property = data.property;

            // Populate edit form
            document.getElementById('editPropertyId').value = property.id;
            document.getElementById('editName').value = property.name || '';
            document.getElementById('editPropertyType').value = property.property_type || '';
            document.getElementById('editDescription').value = property.description || '';
            document.getElementById('editCondition').value = property.condition || '';
            document.getElementById('editLocation').value = property.location || '';
            document.getElementById('editPurchasePrice').value = property.purchase_price || '';
            document.getElementById('editCurrentValue').value = property.current_value || '';
            document.getElementById('editNotes').value = property.notes || '';

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editPropertyModal'));
            modal.show();
        } else {
            alert('Error loading property data: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while loading property data');
    });
}

function deleteProperty(propertyId, propertyName) {
    if (confirm(`Are you sure you want to delete "${propertyName}"?`)) {
        fetch(`/properties/${propertyId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Failed to delete property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the property');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
