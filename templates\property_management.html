{% extends 'base.html' %}

{% block title %}Property Management - Inventory Management System{% endblock %}

{% block extra_css %}
<style>
    .property-card {
        border: none;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .property-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .property-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .property-type-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .condition-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .value-display {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1.25rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .stats-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-label {
        font-size: 0.8rem;
        opacity: 0.9;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }

    .property-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: auto;
    }

    .btn-sm {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        flex: 1;
    }

    .properties-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
    }

    @media (max-width: 768px) {
        .properties-grid {
            grid-template-columns: 1fr;
        }

        .stats-card {
            margin-bottom: 0.75rem;
        }
    }
    
    .modal-lg {
        max-width: 800px;
    }
    
    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .form-row .form-group {
        flex: 1;
    }
    
    .tags-input {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px;
        min-height: 38px;
    }
    
    .tag {
        display: inline-block;
        background: #007bff;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin: 2px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">
                    <i class="fas fa-home me-3"></i>
                    Property Management
                </h1>
                <p class="page-subtitle">Manage your properties, track values, and list for sale</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <button class="add-property-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                    <i class="fas fa-plus"></i>
                    Add New Property
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Overview -->
    <div class="stats-overview">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stat-number">{{ total_properties }}</div>
                    <div class="stat-label">Total Properties</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success">
                    <div class="stat-number">{{ properties_for_sale }}</div>
                    <div class="stat-label">For Sale</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning">
                    <div class="stat-number">${{ total_value|floatformat:0 }}</div>
                    <div class="stat-label">Total Value</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info">
                    <div class="stat-number">{{ insured_properties }}</div>
                    <div class="stat-label">Insured</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="action-bar">
        <div class="search-filter">
            <input type="text" class="search-input" placeholder="Search properties..." id="searchInput">
            <select class="filter-select" id="typeFilter">
                <option value="">All Types</option>
                <option value="house">House</option>
                <option value="apartment">Apartment</option>
                <option value="land">Land</option>
                <option value="commercial">Commercial</option>
                <option value="other">Other</option>
            </select>
            <select class="filter-select" id="saleFilter">
                <option value="">All Properties</option>
                <option value="for_sale">For Sale</option>
                <option value="not_for_sale">Not For Sale</option>
            </select>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="exportProperties()">
                <i class="fas fa-download me-1"></i>Export
            </button>
        </div>
    </div>

    <!-- Properties Grid -->
    {% if properties %}
    <div class="properties-grid" id="propertiesGrid">
        {% for property in properties %}
        <div class="property-card" data-type="{{ property.property_type }}" data-sale="{{ property.for_sale|yesno:'for_sale,not_for_sale' }}">
            <!-- Property Image -->
            <div class="property-image">
                {% if property.images and property.images.0 %}
                    <img src="/media/{{ property.images.0 }}" alt="{{ property.name }}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="no-image" style="display: none;">
                        <i class="fas fa-home"></i>
                    </div>
                {% else %}
                    <div class="no-image">
                        <i class="fas fa-home"></i>
                    </div>
                {% endif %}

                <!-- Status Badges -->
                <div class="property-status">
                    {% if property.for_sale %}
                        <span class="status-badge for-sale">
                            <i class="fas fa-store me-1"></i>For Sale
                        </span>
                    {% endif %}
                    {% if property.insured %}
                        <span class="status-badge" style="background: var(--info-color); color: white;">
                            <i class="fas fa-shield-alt me-1"></i>Insured
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Property Content -->
            <div class="property-content">
                <h3 class="property-title">{{ property.name }}</h3>
                <div class="property-type">
                    <i class="fas fa-tag me-1"></i>{{ property.get_property_type_display }}
                </div>

                <div class="property-details">
                    <div class="detail-item">
                        <div class="detail-label">Current Value</div>
                        <div class="detail-value">
                            {% if property.current_value %}
                                ${{ property.current_value|floatformat:0 }}
                            {% else %}
                                Not set
                            {% endif %}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Condition</div>
                        <div class="detail-value">{{ property.get_condition_display }}</div>
                    </div>
                    {% if property.for_sale %}
                    <div class="detail-item">
                        <div class="detail-label">Sale Price</div>
                        <div class="detail-value" style="color: var(--success-color); font-weight: 700;">
                            ${{ property.sale_price|floatformat:0 }}
                            {% if property.is_negotiable %}
                                <small>(Negotiable)</small>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    <div class="detail-item">
                        <div class="detail-label">Location</div>
                        <div class="detail-value">{{ property.location|default:"Not specified" }}</div>
                    </div>
                </div>
            </div>

            <!-- Property Actions -->
            <div class="property-actions">
                <button class="btn-action btn-view" onclick="viewProperty({{ property.id }})">
                    <i class="fas fa-eye"></i> View
                </button>
                <button class="btn-action btn-edit" onclick="editProperty({{ property.id }})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                {% if property.for_sale %}
                    <button class="btn-action btn-remove-sale" onclick="togglePropertySale({{ property.id }}, false)">
                        <i class="fas fa-store-slash"></i> Remove Sale
                    </button>
                {% else %}
                    <button class="btn-action btn-sale" onclick="togglePropertySale({{ property.id }}, true)">
                        <i class="fas fa-store"></i> For Sale
                    </button>
                {% endif %}
                <button class="btn-action btn-delete" onclick="deleteProperty({{ property.id }}, '{{ property.name }}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-home"></i>
        <h3>No Properties Yet</h3>
        <p>Start building your property portfolio by adding your first property.</p>
        <button class="add-property-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
            <i class="fas fa-plus"></i>
            Add Your First Property
        </button>
    </div>
    {% endif %}
</div>

<!-- Add Property Modal -->
<div class="modal fade" id="addPropertyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.2);">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); color: white; border-radius: 15px 15px 0 0; padding: 1.5rem;">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Add New Property
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form id="addPropertyForm" enctype="multipart/form-data">
                    {% csrf_token %}

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Property Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="property_type" class="form-label">Property Type *</label>
                                    <select class="form-control" id="property_type" name="property_type" required>
                                        <option value="">Select Type</option>
                                        <option value="house">House</option>
                                        <option value="apartment">Apartment</option>
                                        <option value="land">Land</option>
                                        <option value="commercial">Commercial</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Describe your property..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="condition" class="form-label">Condition</label>
                                    <select class="form-control" id="condition" name="condition">
                                        <option value="excellent">Excellent</option>
                                        <option value="good" selected>Good</option>
                                        <option value="fair">Fair</option>
                                        <option value="poor">Poor</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="location" name="location" placeholder="City, State, Country">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Information -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-dollar-sign me-2"></i>Financial Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="purchase_price" class="form-label">Purchase Price</label>
                                    <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="current_value" class="form-label">Current Value</label>
                                    <input type="number" class="form-control" id="current_value" name="current_value" step="0.01" min="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Images -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-images me-2"></i>Property Images
                        </h6>

                        <div class="form-group">
                            <label for="images" class="form-label">Upload Images</label>
                            <input type="file" class="form-control" id="images" name="images" multiple accept="image/*">
                            <div class="form-text">You can select multiple images. Supported formats: JPG, PNG, GIF</div>
                            <div id="imagePreview" class="image-preview"></div>
                        </div>
                    </div>

                    <!-- Sale Information -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-store me-2"></i>Sale Information
                        </h6>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="for_sale" name="for_sale">
                                <label class="form-check-label" for="for_sale">
                                    <strong>Make Available for Sale</strong>
                                </label>
                            </div>
                        </div>

                        <div id="saleFields" class="sale-section" style="display: none;">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="sale_price" class="form-label">Sale Price *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="sale_price" name="sale_price" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_negotiable" name="is_negotiable" checked>
                                            <label class="form-check-label" for="is_negotiable">
                                                Price is negotiable
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="sale_description" class="form-label">Sale Description</label>
                                <textarea class="form-control" id="sale_description" name="sale_description" rows="3" placeholder="Additional description for the sale listing..."></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_phone" class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" placeholder="Phone number for inquiries">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_email" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" placeholder="Email for inquiries">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-clipboard me-2"></i>Additional Information
                        </h6>

                        <div class="form-group">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any additional notes about this property..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 1.5rem; border-top: 1px solid #e9ecef;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="addPropertyForm" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Add Property
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and filter functionality
    const searchInput = document.getElementById('searchInput');
    const typeFilter = document.getElementById('typeFilter');
    const saleFilter = document.getElementById('saleFilter');
    const propertiesGrid = document.getElementById('propertiesGrid');

    function filterProperties() {
        const searchTerm = searchInput.value.toLowerCase();
        const typeValue = typeFilter.value;
        const saleValue = saleFilter.value;
        const propertyCards = propertiesGrid.querySelectorAll('.property-card');

        propertyCards.forEach(card => {
            const title = card.querySelector('.property-title').textContent.toLowerCase();
            const type = card.dataset.type;
            const sale = card.dataset.sale;

            const matchesSearch = title.includes(searchTerm);
            const matchesType = !typeValue || type === typeValue;
            const matchesSale = !saleValue || sale === saleValue;

            if (matchesSearch && matchesType && matchesSale) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    searchInput.addEventListener('input', filterProperties);
    typeFilter.addEventListener('change', filterProperties);
    saleFilter.addEventListener('change', filterProperties);

    // Sale fields toggle
    const forSaleCheckbox = document.getElementById('for_sale');
    const saleFields = document.getElementById('saleFields');

    forSaleCheckbox.addEventListener('change', function() {
        if (this.checked) {
            saleFields.style.display = 'block';
            saleFields.classList.add('active');
            document.getElementById('sale_price').setAttribute('required', 'required');
        } else {
            saleFields.style.display = 'none';
            saleFields.classList.remove('active');
            document.getElementById('sale_price').removeAttribute('required');
        }
    });

    // Image preview functionality
    const imageInput = document.getElementById('images');
    const imagePreview = document.getElementById('imagePreview');

    imageInput.addEventListener('change', function() {
        imagePreview.innerHTML = '';
        const files = this.files;

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.width = '100px';
                    img.style.height = '100px';
                    img.style.objectFit = 'cover';
                    img.style.borderRadius = '8px';
                    img.style.border = '2px solid #e9ecef';
                    imagePreview.appendChild(img);
                };
                reader.readAsDataURL(file);
            }
        }
    });

    // Form submission
    document.getElementById('addPropertyForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch('/property-management/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Property added successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Failed to add property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while adding the property');
        });
    });
});

// Property action functions
function viewProperty(propertyId) {
    window.location.href = `/properties/${propertyId}/`;
}

function editProperty(propertyId) {
    // This would open an edit modal - simplified for now
    alert('Edit functionality - would open edit modal for property ' + propertyId);
}

function deleteProperty(propertyId, propertyName) {
    if (confirm(`Are you sure you want to delete "${propertyName}"? This action cannot be undone.`)) {
        fetch(`/properties/${propertyId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Property deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Failed to delete property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the property');
        });
    }
}

function togglePropertySale(propertyId, makeForSale) {
    if (makeForSale) {
        // Show modal to collect sale information
        const saleModal = `
            <div class="modal fade" id="saleModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Make Property Available for Sale</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="saleForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="salePrice" class="form-label">Sale Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="salePrice" name="sale_price" step="0.01" min="0" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="saleNegotiable" name="is_negotiable" checked>
                                        <label class="form-check-label" for="saleNegotiable">
                                            Price is negotiable
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="saleDescription" class="form-label">Sale Description</label>
                                    <textarea class="form-control" id="saleDescription" name="sale_description" rows="3" placeholder="Additional description for the sale listing..."></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="saleContactPhone" class="form-label">Contact Phone</label>
                                            <input type="tel" class="form-control" id="saleContactPhone" name="contact_phone" placeholder="Phone number">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="saleContactEmail" class="form-label">Contact Email</label>
                                            <input type="email" class="form-control" id="saleContactEmail" name="contact_email" placeholder="Email address">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-success">Make Available for Sale</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('saleModal');
        if (existingModal) existingModal.remove();

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', saleModal);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('saleModal'));
        modal.show();

        // Handle form submission
        document.getElementById('saleForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('for_sale', 'on');

            fetch(`/property/${propertyId}/toggle-sale/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    modal.hide();
                    alert('Property listed for sale successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        });
    } else {
        // Remove from sale
        if (confirm('Are you sure you want to remove this property from sale?')) {
            const formData = new FormData();
            formData.append('for_sale', 'off');

            fetch(`/property/${propertyId}/toggle-sale/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Property removed from sale successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        }
    }
}

function exportProperties() {
    alert('Export functionality - would export properties to CSV/PDF');
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Property Management</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
            <i class="fas fa-plus"></i> Add Property
        </button>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-value">{{ total_properties }}</div>
                <div class="stats-label">Total Properties</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-value">₦{{ total_purchase_value|floatformat:0 }}</div>
                <div class="stats-label">Total Purchase Value</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-value">₦{{ total_current_value|floatformat:0 }}</div>
                <div class="stats-label">Current Value</div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="filter-section">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="Search properties...">
            </div>
            <div class="col-md-4">
                <label for="type" class="form-label">Property Type</label>
                <select class="form-select" id="type" name="type">
                    <option value="">All Types</option>
                    {% for value, label in property_types %}
                        <option value="{{ value }}" {% if selected_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                <a href="{% url 'property_management' %}" class="btn btn-outline-secondary">Clear</a>
            </div>
        </form>
    </div>
    
    <!-- Properties List -->
    <div class="properties-grid">
        {% for property in properties %}
        <div class="property-card">
                <div class="property-header">
                    <h5 class="mb-0">{{ property.name }}</h5>
                    <div>
                        <span class="property-type-badge bg-primary text-white">
                            {{ property.get_property_type_display }}
                        </span>
                        {% if property.for_sale %}
                            <span class="property-type-badge bg-success text-white ms-1">
                                <i class="fas fa-store me-1"></i>For Sale
                            </span>
                        {% endif %}
                    </div>
                </div>
                
                {% if property.description %}
                <p class="text-muted mb-3">{{ property.description|truncatewords:15 }}</p>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Condition</small>
                        <div class="condition-badge 
                            {% if property.condition == 'excellent' %}bg-success text-white
                            {% elif property.condition == 'very_good' %}bg-info text-white
                            {% elif property.condition == 'good' %}bg-primary text-white
                            {% elif property.condition == 'fair' %}bg-warning text-dark
                            {% else %}bg-danger text-white{% endif %}">
                            {{ property.get_condition_display }}
                        </div>
                    </div>
                    <div class="col-6">
                        {% if property.current_value %}
                        <small class="text-muted">Current Value</small>
                        <div class="value-display">{{ property.currency }} {{ property.current_value|floatformat:0 }}</div>
                        {% endif %}
                    </div>
                </div>
                
                {% if property.brand %}
                <div class="mb-2">
                    <small class="text-muted">Brand:</small> {{ property.brand }}
                </div>
                {% endif %}
                
                {% if property.get_tags_list %}
                <div class="mb-3">
                    {% for tag in property.get_tags_list %}
                        <span class="tag">{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <div class="property-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewProperty({{ property.id }})">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editProperty({{ property.id }})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    {% if property.for_sale %}
                        <button class="btn btn-sm btn-success" onclick="togglePropertySale({{ property.id }}, false)" title="Remove from sale">
                            <i class="fas fa-store-slash"></i> Remove Sale
                        </button>
                    {% else %}
                        <button class="btn btn-sm btn-outline-success" onclick="togglePropertySale({{ property.id }}, true)" title="Make available for sale">
                            <i class="fas fa-store"></i> For Sale
                        </button>
                    {% endif %}
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteProperty({{ property.id }}, '{{ property.name }}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        {% empty %}
        <div class="text-center py-5">
            <i class="fas fa-home fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Properties Found</h4>
            <p class="text-muted">Start by adding your first property to track and manage your assets.</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                <i class="fas fa-plus"></i> Add Your First Property
            </button>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Add Property Modal -->
<div class="modal fade" id="addPropertyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Property</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPropertyForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name" class="form-label">Property Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="property_type" class="form-label">Type</label>
                            <select class="form-select" id="property_type" name="property_type">
                                {% for value, label in property_types %}
                                    <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="condition" class="form-label">Condition</label>
                            <select class="form-select" id="condition" name="condition">
                                <option value="excellent">Excellent</option>
                                <option value="very_good">Very Good</option>
                                <option value="good" selected>Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="currency" class="form-label">Currency</label>
                            <select class="form-select" id="currency" name="currency">
                                <option value="NGN">₦ Nigerian Naira</option>
                                <option value="USD">$ US Dollar</option>
                                <option value="EUR">€ Euro</option>
                                <option value="GBP">£ British Pound</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="purchase_price" class="form-label">Purchase Price</label>
                            <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="current_value" class="form-label">Current Value</label>
                            <input type="number" class="form-control" id="current_value" name="current_value" step="0.01">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date">
                        </div>
                        <div class="form-group">
                            <label for="warranty_expiry" class="form-label">Warranty Expiry</label>
                            <input type="date" class="form-control" id="warranty_expiry" name="warranty_expiry">
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="location" class="form-label">Location/Storage</label>
                        <input type="text" class="form-control" id="location" name="location" placeholder="Where is this property located?">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="brand" class="form-label">Brand</label>
                            <input type="text" class="form-control" id="brand" name="brand">
                        </div>
                        <div class="form-group">
                            <label for="model_number" class="form-label">Model Number</label>
                            <input type="text" class="form-control" id="model_number" name="model_number">
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="serial_number" class="form-label">Serial Number</label>
                        <input type="text" class="form-control" id="serial_number" name="serial_number">
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="insured" name="insured">
                        <label class="form-check-label" for="insured">
                            This property is insured
                        </label>
                    </div>
                    
                    <div id="insuranceFields" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="insurance_value" class="form-label">Insurance Value</label>
                                <input type="number" class="form-control" id="insurance_value" name="insurance_value" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="insurance_expiry" class="form-label">Insurance Expiry</label>
                                <input type="date" class="form-control" id="insurance_expiry" name="insurance_expiry">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="tags" class="form-label">Tags</label>
                        <input type="text" class="form-control" id="tags" name="tags" placeholder="Comma-separated tags">
                        <small class="form-text text-muted">e.g., electronics, warranty, expensive</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Image Upload Section -->
                    <div class="form-group mb-3">
                        <label for="images" class="form-label">Property Images</label>
                        <input type="file" class="form-control" id="images" name="images" multiple accept="image/*">
                        <div class="form-text">You can select multiple images. Supported formats: JPG, PNG, GIF</div>
                        <div id="imagePreview" class="mt-2"></div>
                    </div>

                    <!-- Sale Information Section -->
                    <div class="form-group mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="for_sale" name="for_sale">
                            <label class="form-check-label" for="for_sale">
                                <strong>Make Available for Sale</strong>
                            </label>
                        </div>
                    </div>

                    <div id="saleFields" style="display: none;">
                        <div class="card border-success mb-3">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Sale Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="sale_price" class="form-label">Sale Price *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="sale_price" name="sale_price" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="is_negotiable" name="is_negotiable" checked>
                                            <label class="form-check-label" for="is_negotiable">
                                                Price is negotiable
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="sale_description" class="form-label">Sale Description</label>
                                    <textarea class="form-control" id="sale_description" name="sale_description" rows="3" placeholder="Additional description for the sale listing..."></textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="contact_phone" class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" placeholder="Phone number for inquiries">
                                    </div>
                                    <div class="form-group">
                                        <label for="contact_email" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" placeholder="Email for inquiries">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Property</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Property Modal -->
<div class="modal fade" id="editPropertyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Property</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPropertyForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="editPropertyId" name="property_id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editName" class="form-label">Property Name *</label>
                            <input type="text" class="form-control" id="editName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="editPropertyType" class="form-label">Type</label>
                            <select class="form-select" id="editPropertyType" name="property_type">
                                {% for value, label in property_types %}
                                    <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editCondition" class="form-label">Condition</label>
                            <select class="form-select" id="editCondition" name="condition">
                                {% for value, label in conditions %}
                                    <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editLocation" class="form-label">Location</label>
                            <input type="text" class="form-control" id="editLocation" name="location">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editPurchasePrice" class="form-label">Purchase Price</label>
                            <input type="number" class="form-control" id="editPurchasePrice" name="purchase_price" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="editCurrentValue" class="form-label">Current Value</label>
                            <input type="number" class="form-control" id="editCurrentValue" name="current_value" step="0.01">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="editNotes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Image Upload Section -->
                    <div class="form-group mb-3">
                        <label for="editImages" class="form-label">Property Images</label>
                        <input type="file" class="form-control" id="editImages" name="images" multiple accept="image/*">
                        <div class="form-text">You can select multiple images. Supported formats: JPG, PNG, GIF</div>
                        <div id="editImagePreview" class="mt-2"></div>
                        <div id="currentImages" class="mt-2"></div>
                    </div>

                    <!-- Sale Information Section -->
                    <div class="form-group mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editForSale" name="for_sale">
                            <label class="form-check-label" for="editForSale">
                                <strong>Make Available for Sale</strong>
                            </label>
                        </div>
                    </div>

                    <div id="editSaleFields" style="display: none;">
                        <div class="card border-success mb-3">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Sale Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="editSalePrice" class="form-label">Sale Price *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="editSalePrice" name="sale_price" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="editIsNegotiable" name="is_negotiable">
                                            <label class="form-check-label" for="editIsNegotiable">
                                                Price is negotiable
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="editSaleDescription" class="form-label">Sale Description</label>
                                    <textarea class="form-control" id="editSaleDescription" name="sale_description" rows="3" placeholder="Additional description for the sale listing..."></textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="editContactPhone" class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" id="editContactPhone" name="contact_phone" placeholder="Phone number for inquiries">
                                    </div>
                                    <div class="form-group">
                                        <label for="editContactEmail" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="editContactEmail" name="contact_email" placeholder="Email for inquiries">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Property</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Toggle insurance fields
document.getElementById('insured').addEventListener('change', function() {
    const insuranceFields = document.getElementById('insuranceFields');
    insuranceFields.style.display = this.checked ? 'block' : 'none';
});

// Toggle sale fields for add form
document.getElementById('for_sale').addEventListener('change', function() {
    const saleFields = document.getElementById('saleFields');
    saleFields.style.display = this.checked ? 'block' : 'none';

    // Make sale price required when for sale is checked
    const salePriceField = document.getElementById('sale_price');
    if (this.checked) {
        salePriceField.setAttribute('required', 'required');
    } else {
        salePriceField.removeAttribute('required');
    }
});

// Toggle sale fields for edit form
document.addEventListener('change', function(e) {
    if (e.target.id === 'editForSale') {
        const editSaleFields = document.getElementById('editSaleFields');
        editSaleFields.style.display = e.target.checked ? 'block' : 'none';

        // Make sale price required when for sale is checked
        const editSalePriceField = document.getElementById('editSalePrice');
        if (e.target.checked) {
            editSalePriceField.setAttribute('required', 'required');
        } else {
            editSalePriceField.removeAttribute('required');
        }
    }
});

// Image preview functionality
function handleImagePreview(input, previewContainer) {
    const files = input.files;
    previewContainer.innerHTML = '';

    if (files.length > 0) {
        const previewTitle = document.createElement('div');
        previewTitle.className = 'fw-bold mb-2';
        previewTitle.textContent = 'Image Preview:';
        previewContainer.appendChild(previewTitle);

        const imageContainer = document.createElement('div');
        imageContainer.className = 'd-flex flex-wrap gap-2';

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imgWrapper = document.createElement('div');
                    imgWrapper.className = 'position-relative';
                    imgWrapper.style.width = '100px';
                    imgWrapper.style.height = '100px';

                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'img-thumbnail';
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.objectFit = 'cover';

                    imgWrapper.appendChild(img);
                    imageContainer.appendChild(imgWrapper);
                };
                reader.readAsDataURL(file);
            }
        }

        previewContainer.appendChild(imageContainer);
    }
}

// Add image preview listeners
document.getElementById('images').addEventListener('change', function() {
    handleImagePreview(this, document.getElementById('imagePreview'));
});

document.getElementById('editImages').addEventListener('change', function() {
    handleImagePreview(this, document.getElementById('editImagePreview'));
});

// Add property form submission
document.getElementById('addPropertyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{% url "property_management" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to add property'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adding the property');
    });
});

// Edit property form submission
document.getElementById('editPropertyForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const propertyId = document.getElementById('editPropertyId').value;
    const formData = new FormData(this);

    // Add method override for PUT request
    formData.append('_method', 'PUT');

    fetch(`/properties/${propertyId}/`, {
        method: 'POST',  // Use POST with method override
        body: formData,
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to update property'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the property');
    });
});

// Utility functions
function viewProperty(propertyId) {
    window.location.href = `/properties/${propertyId}/`;
}

function editProperty(propertyId) {
    // Fetch property data
    fetch(`/properties/${propertyId}/`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const property = data.property;

            // Populate edit form
            document.getElementById('editPropertyId').value = property.id;
            document.getElementById('editName').value = property.name || '';
            document.getElementById('editPropertyType').value = property.property_type || '';
            document.getElementById('editDescription').value = property.description || '';
            document.getElementById('editCondition').value = property.condition || '';
            document.getElementById('editLocation').value = property.location || '';
            document.getElementById('editPurchasePrice').value = property.purchase_price || '';
            document.getElementById('editCurrentValue').value = property.current_value || '';
            document.getElementById('editNotes').value = property.notes || '';

            // Populate sale fields
            const editForSaleCheckbox = document.getElementById('editForSale');
            const editSaleFields = document.getElementById('editSaleFields');

            editForSaleCheckbox.checked = property.for_sale || false;
            editSaleFields.style.display = property.for_sale ? 'block' : 'none';

            if (property.for_sale) {
                document.getElementById('editSalePrice').value = property.sale_price || '';
                document.getElementById('editIsNegotiable').checked = property.is_negotiable || false;
                document.getElementById('editSaleDescription').value = property.sale_description || '';
                document.getElementById('editContactPhone').value = property.contact_phone || '';
                document.getElementById('editContactEmail').value = property.contact_email || '';
            }

            // Display current images
            const currentImagesDiv = document.getElementById('currentImages');
            currentImagesDiv.innerHTML = '';

            if (property.images && property.images.length > 0) {
                const imagesTitle = document.createElement('div');
                imagesTitle.className = 'fw-bold mb-2';
                imagesTitle.textContent = 'Current Images:';
                currentImagesDiv.appendChild(imagesTitle);

                const imageContainer = document.createElement('div');
                imageContainer.className = 'd-flex flex-wrap gap-2';

                property.images.forEach((imageUrl, index) => {
                    const imgWrapper = document.createElement('div');
                    imgWrapper.className = 'position-relative';
                    imgWrapper.style.width = '100px';
                    imgWrapper.style.height = '100px';

                    const img = document.createElement('img');
                    img.src = imageUrl;
                    img.className = 'img-thumbnail';
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.objectFit = 'cover';

                    imgWrapper.appendChild(img);
                    imageContainer.appendChild(imgWrapper);
                });

                currentImagesDiv.appendChild(imageContainer);
            }

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editPropertyModal'));
            modal.show();
        } else {
            alert('Error loading property data: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while loading property data');
    });
}

function deleteProperty(propertyId, propertyName) {
    if (confirm(`Are you sure you want to delete "${propertyName}"?`)) {
        fetch(`/properties/${propertyId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Failed to delete property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the property');
        });
    }
}

function togglePropertySale(propertyId, makeForSale) {
    if (makeForSale) {
        // Show modal to collect sale information
        const saleModal = `
            <div class="modal fade" id="saleModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Make Property Available for Sale</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="saleForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="salePrice" class="form-label">Sale Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="salePrice" name="sale_price" step="0.01" min="0" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="saleNegotiable" name="is_negotiable" checked>
                                        <label class="form-check-label" for="saleNegotiable">
                                            Price is negotiable
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="saleDescription" class="form-label">Sale Description</label>
                                    <textarea class="form-control" id="saleDescription" name="sale_description" rows="3" placeholder="Additional description for the sale listing..."></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="saleContactPhone" class="form-label">Contact Phone</label>
                                            <input type="tel" class="form-control" id="saleContactPhone" name="contact_phone" placeholder="Phone number">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="saleContactEmail" class="form-label">Contact Email</label>
                                            <input type="email" class="form-control" id="saleContactEmail" name="contact_email" placeholder="Email address">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-success">Make Available for Sale</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('saleModal');
        if (existingModal) existingModal.remove();

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', saleModal);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('saleModal'));
        modal.show();

        // Handle form submission
        document.getElementById('saleForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('for_sale', 'on');

            fetch(`/property/${propertyId}/toggle-sale/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    modal.hide();
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        });
    } else {
        // Remove from sale
        if (confirm('Are you sure you want to remove this property from sale?')) {
            const formData = new FormData();
            formData.append('for_sale', 'off');

            fetch(`/property/${propertyId}/toggle-sale/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        }
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
