{% extends 'base.html' %}

{% block title %}Property Management{% endblock %}

{% block extra_css %}
<style>
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

body {
    background: #f8f9fa;
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 30px 30px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.stats-overview {
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stats-card.success {
    border-left-color: var(--success-color);
}

.stats-card.warning {
    border-left-color: var(--warning-color);
}

.stats-card.info {
    border-left-color: var(--info-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
}

.action-bar {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.add-property-btn {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-property-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    min-width: 250px;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.filter-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    min-width: 150px;
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.property-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.property-image {
    height: 200px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.property-image .no-image {
    color: #6c757d;
    font-size: 3rem;
}

.property-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.status-badge.for-sale {
    background: var(--success-color);
    color: white;
}

.status-badge.not-for-sale {
    background: #6c757d;
    color: white;
}

.property-content {
    padding: 1.5rem;
}

.property-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.property-type {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.property-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.property-actions {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-view {
    background: var(--info-color);
    color: white;
}

.btn-edit {
    background: var(--warning-color);
    color: #212529;
}

.btn-sale {
    background: var(--success-color);
    color: white;
}

.btn-remove-sale {
    background: #6c757d;
    color: white;
}

.btn-delete {
    background: var(--danger-color);
    color: white;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .action-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-filter {
        justify-content: center;
    }
    
    .search-input {
        min-width: 100%;
    }
    
    .properties-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .property-details {
        grid-template-columns: 1fr;
    }
    
    .property-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">
                    <i class="fas fa-home me-3"></i>
                    Property Management
                </h1>
                <p class="page-subtitle">Manage your properties, track values, and list for sale</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <button class="add-property-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                    <i class="fas fa-plus"></i>
                    Add New Property
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Overview -->
    <div class="stats-overview">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stat-number">{{ total_properties }}</div>
                    <div class="stat-label">Total Properties</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success">
                    <div class="stat-number">{{ properties_for_sale }}</div>
                    <div class="stat-label">For Sale</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning">
                    <div class="stat-number">${{ total_value|floatformat:0 }}</div>
                    <div class="stat-label">Total Value</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info">
                    <div class="stat-number">{{ insured_properties }}</div>
                    <div class="stat-label">Insured</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="action-bar">
        <div class="search-filter">
            <input type="text" class="search-input" placeholder="Search properties..." id="searchInput">
            <select class="filter-select" id="typeFilter">
                <option value="">All Types</option>
                <option value="house">House</option>
                <option value="apartment">Apartment</option>
                <option value="land">Land</option>
                <option value="commercial">Commercial</option>
                <option value="other">Other</option>
            </select>
            <select class="filter-select" id="saleFilter">
                <option value="">All Properties</option>
                <option value="for_sale">For Sale</option>
                <option value="not_for_sale">Not For Sale</option>
            </select>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="exportProperties()">
                <i class="fas fa-download me-1"></i>Export
            </button>
        </div>
    </div>

    <!-- Properties Grid -->
    {% if properties %}
    <div class="properties-grid" id="propertiesGrid">
        {% for property in properties %}
        <div class="property-card" data-type="{{ property.property_type }}" data-sale="{{ property.for_sale|yesno:'for_sale,not_for_sale' }}">
            <!-- Property Image -->
            <div class="property-image">
                {% if property.images and property.images.0 %}
                    <img src="/media/{{ property.images.0 }}" alt="{{ property.name }}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="no-image" style="display: none;">
                        <i class="fas fa-home"></i>
                    </div>
                {% else %}
                    <div class="no-image">
                        <i class="fas fa-home"></i>
                    </div>
                {% endif %}

                <!-- Status Badges -->
                <div class="property-status">
                    {% if property.for_sale %}
                        <span class="status-badge for-sale">
                            <i class="fas fa-store me-1"></i>For Sale
                        </span>
                    {% endif %}
                    {% if property.insured %}
                        <span class="status-badge" style="background: var(--info-color); color: white;">
                            <i class="fas fa-shield-alt me-1"></i>Insured
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Property Content -->
            <div class="property-content">
                <h3 class="property-title">{{ property.name }}</h3>
                <div class="property-type">
                    <i class="fas fa-tag me-1"></i>{{ property.get_property_type_display }}
                </div>

                <div class="property-details">
                    <div class="detail-item">
                        <div class="detail-label">Current Value</div>
                        <div class="detail-value">
                            {% if property.current_value %}
                                ${{ property.current_value|floatformat:0 }}
                            {% else %}
                                Not set
                            {% endif %}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Condition</div>
                        <div class="detail-value">{{ property.get_condition_display }}</div>
                    </div>
                    {% if property.for_sale %}
                    <div class="detail-item">
                        <div class="detail-label">Sale Price</div>
                        <div class="detail-value" style="color: var(--success-color); font-weight: 700;">
                            ${{ property.sale_price|floatformat:0 }}
                            {% if property.is_negotiable %}
                                <small>(Negotiable)</small>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    <div class="detail-item">
                        <div class="detail-label">Location</div>
                        <div class="detail-value">{{ property.location|default:"Not specified" }}</div>
                    </div>
                </div>
            </div>

            <!-- Property Actions -->
            <div class="property-actions">
                <button class="btn-action btn-view" onclick="viewProperty({{ property.id }})">
                    <i class="fas fa-eye"></i> View
                </button>
                <button class="btn-action btn-edit" onclick="editProperty({{ property.id }})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                {% if property.for_sale %}
                    <button class="btn-action btn-remove-sale" onclick="togglePropertySale({{ property.id }}, false)">
                        <i class="fas fa-store-slash"></i> Remove Sale
                    </button>
                {% else %}
                    <button class="btn-action btn-sale" onclick="togglePropertySale({{ property.id }}, true)">
                        <i class="fas fa-store"></i> For Sale
                    </button>
                {% endif %}
                <button class="btn-action btn-delete" onclick="deleteProperty({{ property.id }}, '{{ property.name }}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-home"></i>
        <h3>No Properties Yet</h3>
        <p>Start building your property portfolio by adding your first property.</p>
        <button class="add-property-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
            <i class="fas fa-plus"></i>
            Add Your First Property
        </button>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and filter functionality
    const searchInput = document.getElementById('searchInput');
    const typeFilter = document.getElementById('typeFilter');
    const saleFilter = document.getElementById('saleFilter');
    const propertiesGrid = document.getElementById('propertiesGrid');

    function filterProperties() {
        if (!propertiesGrid) return;

        const searchTerm = searchInput.value.toLowerCase();
        const typeValue = typeFilter.value;
        const saleValue = saleFilter.value;
        const propertyCards = propertiesGrid.querySelectorAll('.property-card');

        propertyCards.forEach(card => {
            const title = card.querySelector('.property-title').textContent.toLowerCase();
            const type = card.dataset.type;
            const sale = card.dataset.sale;

            const matchesSearch = title.includes(searchTerm);
            const matchesType = !typeValue || type === typeValue;
            const matchesSale = !saleValue || sale === saleValue;

            if (matchesSearch && matchesType && matchesSale) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    if (searchInput) searchInput.addEventListener('input', filterProperties);
    if (typeFilter) typeFilter.addEventListener('change', filterProperties);
    if (saleFilter) saleFilter.addEventListener('change', filterProperties);
});

// Property action functions
function viewProperty(propertyId) {
    window.location.href = `/properties/${propertyId}/`;
}

function editProperty(propertyId) {
    // Redirect to property detail page with edit mode
    window.location.href = `/properties/${propertyId}/?edit=true`;
}

function deleteProperty(propertyId, propertyName) {
    if (confirm(`Are you sure you want to delete "${propertyName}"? This action cannot be undone.`)) {
        fetch(`/properties/${propertyId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Property deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Failed to delete property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the property');
        });
    }
}

function togglePropertySale(propertyId, makeForSale) {
    if (makeForSale) {
        // Redirect to property detail page with sale mode
        window.location.href = `/properties/${propertyId}/?sale=true`;
    } else {
        // Remove from sale
        if (confirm('Are you sure you want to remove this property from sale?')) {
            const formData = new FormData();
            formData.append('for_sale', 'false');

            fetch(`/property/${propertyId}/toggle-sale/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Property removed from sale successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        }
    }
}

function exportProperties() {
    alert('Export functionality - would export properties to CSV/PDF');
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
