{% extends 'base.html' %}

{% block title %}Property Management{% endblock %}

{% block extra_css %}
<style>
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

body {
    background: #f8f9fa;
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 30px 30px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.stats-overview {
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stats-card.success {
    border-left-color: var(--success-color);
}

.stats-card.warning {
    border-left-color: var(--warning-color);
}

.stats-card.info {
    border-left-color: var(--info-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
}

.action-bar {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.add-property-btn {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-property-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    min-width: 250px;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.filter-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    min-width: 150px;
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.property-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.property-image {
    height: 200px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.property-image .no-image {
    color: #6c757d;
    font-size: 3rem;
}

.property-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.status-badge.for-sale {
    background: var(--success-color);
    color: white;
}

.status-badge.not-for-sale {
    background: #6c757d;
    color: white;
}

.property-content {
    padding: 1.5rem;
}

.property-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.property-type {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.property-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.property-actions {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-view {
    background: var(--info-color);
    color: white;
}

.btn-edit {
    background: var(--warning-color);
    color: #212529;
}

.btn-sale {
    background: var(--success-color);
    color: white;
}

.btn-remove-sale {
    background: #6c757d;
    color: white;
}

.btn-delete {
    background: var(--danger-color);
    color: white;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.form-section {
    margin-bottom: 2rem;
}

.form-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.image-preview {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.sale-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.sale-section.active {
    background: #e8f5e8;
    border: 2px solid var(--success-color);
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .action-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-filter {
        justify-content: center;
    }
    
    .search-input {
        min-width: 100%;
    }
    
    .properties-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .property-details {
        grid-template-columns: 1fr;
    }
    
    .property-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">
                    <i class="fas fa-home me-3"></i>
                    Property Management
                </h1>
                <p class="page-subtitle">Manage your properties, track values, and list for sale</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <button class="add-property-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                    <i class="fas fa-plus"></i>
                    Add New Property
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Overview -->
    <div class="stats-overview">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stat-number">{{ total_properties }}</div>
                    <div class="stat-label">Total Properties</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success">
                    <div class="stat-number">{{ properties.count }}</div>
                    <div class="stat-label">Properties Listed</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning">
                    <div class="stat-number">${{ total_current_value|floatformat:0 }}</div>
                    <div class="stat-label">Total Value</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info">
                    <div class="stat-number">${{ total_purchase_value|floatformat:0 }}</div>
                    <div class="stat-label">Purchase Value</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="action-bar">
        <div class="search-filter">
            <input type="text" class="search-input" placeholder="Search properties..." id="searchInput">
            <select class="filter-select" id="typeFilter">
                <option value="">All Types</option>
                <option value="house">House</option>
                <option value="apartment">Apartment</option>
                <option value="land">Land</option>
                <option value="commercial">Commercial</option>
                <option value="other">Other</option>
            </select>
            <select class="filter-select" id="saleFilter">
                <option value="">All Properties</option>
                <option value="for_sale">For Sale</option>
                <option value="not_for_sale">Not For Sale</option>
            </select>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="exportProperties()">
                <i class="fas fa-download me-1"></i>Export
            </button>
        </div>
    </div>

    <!-- Properties Grid -->
    {% if properties %}
    <div class="properties-grid" id="propertiesGrid">
        {% for property in properties %}
        <div class="property-card" data-type="{{ property.property_type }}" data-sale="{{ property.for_sale|yesno:'for_sale,not_for_sale' }}">
            <!-- Property Image -->
            <div class="property-image">
                {% if property.images and property.images.0 %}
                    <img src="/media/{{ property.images.0 }}" alt="{{ property.name }}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="no-image" style="display: none;">
                        <i class="fas fa-home"></i>
                    </div>
                {% else %}
                    <div class="no-image">
                        <i class="fas fa-home"></i>
                    </div>
                {% endif %}

                <!-- Status Badges -->
                <div class="property-status">
                    {% if property.for_sale %}
                        <span class="status-badge for-sale">
                            <i class="fas fa-store me-1"></i>For Sale
                        </span>
                    {% endif %}
                    {% if property.insured %}
                        <span class="status-badge" style="background: var(--info-color); color: white;">
                            <i class="fas fa-shield-alt me-1"></i>Insured
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Property Content -->
            <div class="property-content">
                <h3 class="property-title">{{ property.name }}</h3>
                <div class="property-type">
                    <i class="fas fa-tag me-1"></i>{{ property.get_property_type_display }}
                </div>

                <div class="property-details">
                    <div class="detail-item">
                        <div class="detail-label">Current Value</div>
                        <div class="detail-value">
                            {% if property.current_value %}
                                ${{ property.current_value|floatformat:0 }}
                            {% else %}
                                Not set
                            {% endif %}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Condition</div>
                        <div class="detail-value">{{ property.get_condition_display }}</div>
                    </div>
                    {% if property.for_sale %}
                    <div class="detail-item">
                        <div class="detail-label">Sale Price</div>
                        <div class="detail-value" style="color: var(--success-color); font-weight: 700;">
                            ${{ property.sale_price|floatformat:0 }}
                            {% if property.is_negotiable %}
                                <small>(Negotiable)</small>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    <div class="detail-item">
                        <div class="detail-label">Location</div>
                        <div class="detail-value">{{ property.location|default:"Not specified" }}</div>
                    </div>
                </div>
            </div>

            <!-- Property Actions -->
            <div class="property-actions">
                <button class="btn-action btn-view" onclick="viewProperty({{ property.id }})">
                    <i class="fas fa-eye"></i> View
                </button>
                <button class="btn-action btn-edit" onclick="editProperty({{ property.id }})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                {% if property.for_sale %}
                    <button class="btn-action btn-remove-sale" onclick="togglePropertySale({{ property.id }}, false)">
                        <i class="fas fa-store-slash"></i> Remove Sale
                    </button>
                {% else %}
                    <button class="btn-action btn-sale" onclick="togglePropertySale({{ property.id }}, true)">
                        <i class="fas fa-store"></i> For Sale
                    </button>
                {% endif %}
                <button class="btn-action btn-delete" onclick="deleteProperty({{ property.id }}, '{{ property.name }}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <i class="fas fa-home"></i>
        <h3>No Properties Yet</h3>
        <p>Start building your property portfolio by adding your first property.</p>
        <button class="add-property-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
            <i class="fas fa-plus"></i>
            Add Your First Property
        </button>
    </div>
    {% endif %}
</div>

<!-- Add Property Modal -->
<div class="modal fade" id="addPropertyModal" tabindex="-1" aria-labelledby="addPropertyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.2);">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); color: white; border-radius: 15px 15px 0 0; padding: 1.5rem;">
                <h5 class="modal-title" id="addPropertyModalLabel">
                    <i class="fas fa-plus me-2"></i>Add New Property
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form id="addPropertyForm" enctype="multipart/form-data">
                    {% csrf_token %}

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Property Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="property_type" class="form-label">Property Type *</label>
                                    <select class="form-control" id="property_type" name="property_type" required>
                                        <option value="">Select Type</option>
                                        <option value="house">House</option>
                                        <option value="apartment">Apartment</option>
                                        <option value="land">Land</option>
                                        <option value="commercial">Commercial</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Describe your property..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="condition" class="form-label">Condition</label>
                                    <select class="form-control" id="condition" name="condition">
                                        <option value="excellent">Excellent</option>
                                        <option value="good" selected>Good</option>
                                        <option value="fair">Fair</option>
                                        <option value="poor">Poor</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="location" name="location" placeholder="City, State, Country">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Information -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-dollar-sign me-2"></i>Financial Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="purchase_price" class="form-label">Purchase Price</label>
                                    <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="current_value" class="form-label">Current Value</label>
                                    <input type="number" class="form-control" id="current_value" name="current_value" step="0.01" min="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Images -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-images me-2"></i>Property Images
                        </h6>

                        <div class="form-group">
                            <label for="images" class="form-label">Upload Images</label>
                            <input type="file" class="form-control" id="images" name="images" multiple accept="image/*">
                            <div class="form-text">You can select multiple images. Supported formats: JPG, PNG, GIF</div>
                            <div id="imagePreview" class="image-preview"></div>
                        </div>
                    </div>

                    <!-- Sale Information -->
                    <div class="form-section">
                        <h6 class="form-section-title">
                            <i class="fas fa-store me-2"></i>Sale Information
                        </h6>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="for_sale" name="for_sale">
                                <label class="form-check-label" for="for_sale">
                                    <strong>Make Available for Sale</strong>
                                </label>
                            </div>
                        </div>

                        <div id="saleFields" class="sale-section" style="display: none;">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="sale_price" class="form-label">Sale Price *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="sale_price" name="sale_price" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_negotiable" name="is_negotiable" checked>
                                            <label class="form-check-label" for="is_negotiable">
                                                Price is negotiable
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="sale_description" class="form-label">Sale Description</label>
                                <textarea class="form-control" id="sale_description" name="sale_description" rows="3" placeholder="Additional description for the sale listing..."></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_phone" class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" placeholder="Phone number for inquiries">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_email" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" placeholder="Email for inquiries">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 1.5rem; border-top: 1px solid #e9ecef;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="addPropertyForm" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Add Property
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and filter functionality
    const searchInput = document.getElementById('searchInput');
    const typeFilter = document.getElementById('typeFilter');
    const saleFilter = document.getElementById('saleFilter');
    const propertiesGrid = document.getElementById('propertiesGrid');

    function filterProperties() {
        if (!propertiesGrid) return;

        const searchTerm = searchInput.value.toLowerCase();
        const typeValue = typeFilter.value;
        const saleValue = saleFilter.value;
        const propertyCards = propertiesGrid.querySelectorAll('.property-card');

        propertyCards.forEach(card => {
            const title = card.querySelector('.property-title').textContent.toLowerCase();
            const type = card.dataset.type;
            const sale = card.dataset.sale;

            const matchesSearch = title.includes(searchTerm);
            const matchesType = !typeValue || type === typeValue;
            const matchesSale = !saleValue || sale === saleValue;

            if (matchesSearch && matchesType && matchesSale) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    if (searchInput) searchInput.addEventListener('input', filterProperties);
    if (typeFilter) typeFilter.addEventListener('change', filterProperties);
    if (saleFilter) saleFilter.addEventListener('change', filterProperties);

    // Sale fields toggle for add property modal
    const forSaleCheckbox = document.getElementById('for_sale');
    const saleFields = document.getElementById('saleFields');

    if (forSaleCheckbox) {
        forSaleCheckbox.addEventListener('change', function() {
            if (this.checked) {
                saleFields.style.display = 'block';
                saleFields.classList.add('active');
                document.getElementById('sale_price').setAttribute('required', 'required');
            } else {
                saleFields.style.display = 'none';
                saleFields.classList.remove('active');
                document.getElementById('sale_price').removeAttribute('required');
            }
        });
    }

    // Image preview functionality
    const imageInput = document.getElementById('images');
    const imagePreview = document.getElementById('imagePreview');

    if (imageInput) {
        imageInput.addEventListener('change', function() {
            imagePreview.innerHTML = '';
            const files = this.files;

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.style.width = '100px';
                        img.style.height = '100px';
                        img.style.objectFit = 'cover';
                        img.style.borderRadius = '8px';
                        img.style.border = '2px solid #e9ecef';
                        imagePreview.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                }
            }
        });
    }

    // Form submission for add property
    const addPropertyForm = document.getElementById('addPropertyForm');
    if (addPropertyForm) {
        addPropertyForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = document.querySelector('button[form="addPropertyForm"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding Property...';
            submitBtn.disabled = true;

            fetch('/property-management/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addPropertyModal'));
                    modal.hide();

                    // Show success message
                    showAlert('Property added successfully!', 'success');

                    // Reset form
                    addPropertyForm.reset();
                    imagePreview.innerHTML = '';
                    saleFields.style.display = 'none';
                    saleFields.classList.remove('active');

                    // Reload page to show new property
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('Error: ' + (data.error || 'Failed to add property'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while adding the property', 'error');
            })
            .finally(() => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
});

// Property action functions
function viewProperty(propertyId) {
    window.location.href = `/properties/${propertyId}/`;
}

function editProperty(propertyId) {
    // Redirect to property detail page with edit mode
    window.location.href = `/properties/${propertyId}/?edit=true`;
}

function deleteProperty(propertyId, propertyName) {
    if (confirm(`Are you sure you want to delete "${propertyName}"? This action cannot be undone.`)) {
        fetch(`/properties/${propertyId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Property deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Failed to delete property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the property');
        });
    }
}

function togglePropertySale(propertyId, makeForSale) {
    if (makeForSale) {
        // Redirect to property detail page with sale mode
        window.location.href = `/properties/${propertyId}/?sale=true`;
    } else {
        // Remove from sale
        if (confirm('Are you sure you want to remove this property from sale?')) {
            const formData = new FormData();
            formData.append('for_sale', 'false');

            fetch(`/property/${propertyId}/toggle-sale/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Property removed from sale successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        }
    }
}

function exportProperties() {
    alert('Export functionality - would export properties to CSV/PDF');
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Helper function to show alerts
function showAlert(message, type) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
    `;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}
