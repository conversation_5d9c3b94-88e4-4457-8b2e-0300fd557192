<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Modern Invoice</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 700px; margin: auto; padding: 20px; background: #f9f9f9; color: #444; }
    h1 { text-align: center; color: #1abc9c; border-bottom: 3px solid #1abc9c; padding-bottom: 10px; }
    .company-info { text-align: center; margin-bottom: 30px; font-size: 1em; color: #7f8c8d; }
    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
    th, td { border-bottom: 1px solid #ddd; padding: 12px 15px; text-align: left; }
    th { background-color: #1abc9c; color: white; }
    tr:nth-child(even) { background-color: #f2f2f2; }
    .total { text-align: right; font-weight: bold; font-size: 1.3em; padding-right: 15px; margin-top: 10px; }
    .footer { margin-top: 40px; text-align: center; font-size: 0.9em; color: #999; }
    .footer img { max-height: 120px; display: block; margin: 10px auto; }
  </style>
</head>
<body>
  <h1>Invoice</h1>
  <div class="company-info">
    <p>{{ company_name }}</p>
    <p>Generated on {{ created_at }}</p>
  </div>
  <table>
    <thead>
      <tr>
        <th>SKU</th>
        <th>Description</th>
        <th>Quantity</th>
        <th>Unit Price</th>
        <th>Line Total</th>
      </tr>
    </thead>
    <tbody>
      {% for item in items %}
      <tr>
        <td>{{ item.sku|default:"N/A" }}</td>
        <td>{{ item.name }}</td>
        <td>{{ item.quantity }}</td>
        <td>{{ item.unit_price }}</td>
        <td>{{ item.total_price }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  <p class="total">Total Amount Due: {{ total_price }}</p>
  <div class="footer">
    <p>Authorized Signature:</p>
    {% if signature_url %}
      <img src="{{ signature_url }}" alt="Signature" />
    {% else %}
      <p>____________________</p>
    {% endif %}
    <p>Company Stamp:</p>
    {% if stamp_url %}
      <img src="{{ stamp_url }}" alt="Stamp" />
    {% else %}
      <p>____________________</p>
    {% endif %}
  </div>
</body>
</html>
