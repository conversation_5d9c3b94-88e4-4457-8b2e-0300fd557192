{% extends 'base.html' %}

{% block title %}{{ goods.name }} - Activity History{% endblock %}

{% block extra_css %}
<style>
.activity-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.product-info {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    display: block;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.filters-section {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.activity-timeline {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.activity-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #adb5bd;
}

.activity-time {
    font-weight: 500;
}

.activity-user {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.value-change {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 6px;
    margin: 0.5rem 0;
    font-family: monospace;
    font-size: 0.85rem;
}

.old-value {
    color: #dc3545;
    text-decoration: line-through;
}

.new-value {
    color: #28a745;
    font-weight: 600;
}

.quantity-change {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.quantity-positive {
    background: #d4edda;
    color: #155724;
}

.quantity-negative {
    background: #f8d7da;
    color: #721c24;
}

.no-activities {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.pagination-wrapper {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
}

@media (max-width: 768px) {
    .activity-header {
        padding: 1.5rem;
    }

    .stats-cards {
        grid-template-columns: 1fr 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .activity-icon {
        margin-bottom: 0.5rem;
        margin-right: 0;
    }

    .activity-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Activity Header -->
    <div class="activity-header">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1 class="mb-2">Activity History</h1>
                <h3 class="mb-0 opacity-75">{{ goods.name }}</h3>
            </div>
            <a href="{% url 'goods_management' %}" class="btn btn-light">
                <i class="fas fa-arrow-left me-1"></i>Back to Products
            </a>
        </div>

        <div class="product-info">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-1"><strong>Category:</strong> {{ goods.category.name|default:"Uncategorized" }}</p>
                    <p class="mb-1"><strong>Store:</strong> {{ goods.store.name|default:"No Store" }}</p>
                    <p class="mb-0"><strong>Current Stock:</strong> {{ goods.quantity }} units</p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>Price:</strong> {{ goods.currency_symbol }}{{ goods.price }}</p>
                    <p class="mb-1"><strong>Status:</strong> {{ goods.get_stock_status_display }}</p>
                    <p class="mb-0"><strong>Created:</strong> {{ goods.created_at|date:"M d, Y" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <span class="stat-number">{{ total_activities }}</span>
            <div class="stat-label">Total Activities</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ stock_changes }}</span>
            <div class="stat-label">Stock Changes</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ price_changes }}</span>
            <div class="stat-label">Price Updates</div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <h5 class="mb-3">Filter Activities</h5>
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="activity_type" class="form-label">Activity Type</label>
                <select name="activity_type" id="activity_type" class="form-select">
                    <option value="">All Types</option>
                    {% for value, label in activity_types %}
                        <option value="{{ value }}" {% if filters.activity_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" name="date_from" id="date_from" class="form-control" value="{{ filters.date_from }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" name="date_to" id="date_to" class="form-control" value="{{ filters.date_to }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                    <a href="{% url 'goods_activity_history' goods.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Activity Timeline -->
    <div class="activity-timeline">
        <h5 class="mb-4">Activity Timeline</h5>

        {% if activities %}
            {% for activity in activities %}
            <div class="activity-item">
                <div class="activity-icon bg-{{ activity.color_class }}">
                    <i class="fas fa-{{ activity.icon }} text-white"></i>
                </div>

                <div class="activity-content">
                    <div class="activity-title">{{ activity.title }}</div>
                    <div class="activity-description">{{ activity.description }}</div>

                    {% if activity.old_value and activity.new_value %}
                    <div class="value-change">
                        <div class="old-value">Old: {{ activity.old_value }}</div>
                        <div class="new-value">New: {{ activity.new_value }}</div>
                    </div>
                    {% endif %}

                    {% if activity.quantity_change != 0 %}
                    <span class="quantity-change {% if activity.quantity_change > 0 %}quantity-positive{% else %}quantity-negative{% endif %}">
                        {% if activity.quantity_change > 0 %}+{% endif %}{{ activity.quantity_change }} units
                    </span>
                    {% endif %}

                    <div class="activity-meta">
                        <span class="activity-time">{{ activity.created_at|date:"M d, Y g:i A" }}</span>
                        <span class="activity-user">{{ activity.created_by.get_full_name|default:activity.created_by.username }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}


            <!-- Pagination -->
            {% if activities.has_other_pages %}
            <div class="pagination-wrapper">
                <nav aria-label="Activity pagination">
                    <ul class="pagination">
                        {% if activities.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ activities.previous_page_number }}{% if filters.activity_type %}&activity_type={{ filters.activity_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        {% for num in activities.paginator.page_range %}
                            {% if activities.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > activities.number|add:'-3' and num < activities.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if filters.activity_type %}&activity_type={{ filters.activity_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if activities.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ activities.next_page_number }}{% if filters.activity_type %}&activity_type={{ filters.activity_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="no-activities">
                <i class="fas fa-history fa-3x mb-3 text-muted"></i>
                <h5>No Activities Found</h5>
                <p class="text-muted">No activities match your current filters, or this product has no recorded activities yet.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
