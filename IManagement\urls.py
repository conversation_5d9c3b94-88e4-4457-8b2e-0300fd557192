from django.conf import settings
from django.conf.urls.static import static
from django.urls import path
from . import views
from .views import (
    ManagedGoodView,
    SingleGoodView,
    restock_good,
    net_total_value,
    sell_good,
)

urlpatterns = [
    # Home
    path('', views.home, name='home'),

    # Auth
    path('register/', views.register, name='register'),
    path('login/', views.user_login, name='login'),
    path('logout/', views.user_logout, name='logout'),
    path('accounts/login/', views.user_login, name='login'),  # fallback login redirect
    path('auth/me/', views.get_authenticated_user, name='get_authenticated_user'),
    path('forget-password/', views.forget_password, name='forget_password'),
    path('reset-password/<str:uidb64>/<str:token>/', views.reset_password, name='reset_password'),

    # Dashboard
    path('dashboard/', views.dashboard, name='dashboard'),
    path('dashboard/stores/', views.user_stores_list, name='user_stores_list'),

    # Country/State API
    path('api/countries/', views.get_countries, name='get_countries'),
    path('api/states/<int:country_id>/', views.get_states, name='get_states'),

    # Store Management
    path('stores/', views.stores_list_create, name='stores_list_create'),
    path('stores/<int:store_id>/', views.store_detail, name='store_detail'),
    path('stores/<int:store_id>/add-users/', views.store_add_users, name='store_add_users'),
    path('stores/<int:store_id>/remove-users/', views.remove_users_from_store, name='remove_users_from_store'),
    path('stores/user-stores/', views.user_stores_list, name='user_stores_list'),
    path('authorized-stores/', views.list_authorized_stores, name='list_authorized_stores'),

    # Goods (Seller)
    path('goods/', views.goods_list_create, name='goods_list_create'),
    path('goods/<int:goods_id>/', views.goods_detail, name='goods_detail'),
    path('goods/<int:goods_id>/update/', views.update_goods, name='update_goods'),
    path('goods/<int:goods_id>/seller-contact/', views.get_seller_contact, name='get_seller_contact'),
    path('goods/<int:goods_id>/contact-seller/', views.contact_seller_buy_goods, name='contact_seller_buy_goods'),

    # Search
    path('search/', views.search_goods, name='search_goods'),

    # Category Management
    path('categories/', views.list_categories, name='list_categories'),  # seller view
    path('categories/create/', views.create_category, name='create_category'),
    path('categories/<int:category_id>/update/', views.update_category, name='update_category'),
    path('categories/<int:category_id>/delete/', views.delete_category, name='delete_category'),
    path('categories/seller/', views.seller_categories, name='seller_categories'),  # same as above
    path('categories/public/', views.public_categories, name='public_categories'),

    # Public Goods
    path('public/goods/', views.public_goods_list, name='public_goods_list'),
    path('public/goods/<int:goods_id>/', views.public_goods_detail, name='public_goods_detail'),
    path('public/goods/detail/<int:goods_id>/', views.public_goods_detail, name='public_goods_detail'),

    # Contact seller (redundant aliases)
    path('contact/<int:goods_id>/', views.get_seller_contact, name='get_seller_contact'),
    path('buy/<int:goods_id>/contact/', views.contact_seller_buy_goods, name='contact_seller_buy_goods'),

    # Managed Goods
    path('goods-management/', ManagedGoodView.as_view(), name='goods-management'),
    path('goods-management/<int:good_id>/', SingleGoodView.as_view(), name='single-good'),
    path('goods-management/<int:good_id>/restock/', restock_good, name='restock-good'),
    path('goods-management/<int:good_id>/sell/', sell_good, name='sell-good'),
    path('goods-management/net-total/', net_total_value, name='net-total-value'),

    # Receipts
    path('receipt-templates/', views.receipt_templates_list, name='receipt_templates_list'),
    path('receipt-templates/<int:template_id>/', views.receipt_template_preview, name='receipt_template_preview'),
    path('receipts/create/', views.create_receipt, name='create_receipt'),
    path('receipts/', views.list_user_receipts, name='list_user_receipts'),
    path('receipts/<int:receipt_id>/download/', views.download_receipt, name='download_receipt'),

    # Invoices
    path('invoice-templates/', views.invoice_templates_list, name='invoice_templates_list'),
    path('invoice-templates/<int:template_id>/', views.invoice_template_preview, name='invoice_template_preview'),
    path('invoices/create/', views.create_invoice, name='create_invoice'),
    path('invoices/', views.list_user_invoices, name='list_user_invoices'),
    path('invoices/<int:invoice_id>/download/', views.download_invoice, name='download_invoice'),

    # Notifications
    path('notifications/', views.get_user_notifications, name='get_user_notifications'),
    path('notifications/mark-read/', views.mark_notification_read, name='mark_notification_read'),

    # Stats API
    path('api/stores/count/', views.api_store_count, name='api_store_count'),
    path('api/goods/count/', views.api_goods_count, name='api_goods_count'),
    path('api/categories/count/', views.api_categories_count, name='api_categories_count'),
    path('api/receipts/count/', views.api_receipts_count, name='api_receipts_count'),
    path('api/invoices/count/', views.api_invoices_count, name='api_invoices_count'),
    path('api/properties/count/', views.api_properties_count, name='api_properties_count'),
    path('api/user/stores/', views.api_user_stores, name='api_user_stores'),

    # User Profile
    path('profile/', views.user_profile, name='user_profile'),

    #Receipt mail and invoice mail
    path('receipts/<int:receipt_id>/email/', views.send_receipt_email, name='send_receipt_email'),
    path('invoices/<int:invoice_id>/email/', views.send_invoice_email, name='send_invoice_email'),

    # Enhanced Authentication with Email Verification
    path('enhanced-register/', views.enhanced_register, name='enhanced_register'),
    path('verify-email/', views.verify_email, name='verify_email'),
    path('resend-verification/', views.resend_verification_code, name='resend_verification_code'),

    # AJAX endpoints
    path('get-countries/', views.get_countries, name='get_countries'),
    path('get-states/<int:country_id>/', views.get_states, name='get_states'),

    # Enhanced Goods Management and Filtering (COMMENTED OUT)
    # path('enhanced-goods/', views.enhanced_public_goods_list, name='enhanced_public_goods_list'),
    path('manage-inventory/', views.manage_goods_inventory, name='manage_goods_inventory'),
    path('goods-activity-history/', views.all_goods_activity_history, name='all_goods_activity_history'),
    path('goods/<int:goods_id>/detail/', views.goods_detail_with_activity, name='goods_detail_with_activity'),

    # Private Seller Store
    path('private-seller-store/', views.private_seller_store, name='private_seller_store'),
    path('seller/<int:seller_id>/store/', views.public_seller_store, name='private_seller_store_public'),

    # Store Sharing
    path('stores/<int:store_id>/share/create/', views.create_store_share, name='create_store_share'),
    path('stores/<int:store_id>/share/email/', views.share_store_via_email, name='share_store_via_email'),
    path('stores/<int:store_id>/shares/', views.list_store_shares, name='list_store_shares'),
    path('shared/<str:token>/', views.shared_store_view, name='shared_store_view'),

    # Goods Activity History
    path('goods/<int:goods_id>/activity/', views.goods_activity_history, name='goods_activity_history'),

    # Email Verification Login
    path('login/', views.custom_login, name='login'),
    path('verify-email/', views.verify_email_code, name='verify_email_code'),
    path('trusted-devices/', views.manage_trusted_devices, name='manage_trusted_devices'),

    # Contact Support
    path('contact-support/', views.contact_support, name='contact_support'),
    path('my-tickets/', views.my_support_tickets, name='my_support_tickets'),
    path('tickets/<int:ticket_id>/', views.support_ticket_detail, name='support_ticket_detail'),

    # Admin Views (Staff Only)
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/users/', views.admin_users, name='admin_users'),
    path('admin/goods/', views.admin_goods, name='admin_goods'),
    path('admin/stores/', views.admin_stores, name='admin_stores'),
    path('admin/properties/', views.admin_properties, name='admin_properties'),
    path('admin/tickets/', views.admin_support_tickets, name='admin_support_tickets'),
    path('admin/stats/', views.admin_system_stats, name='admin_system_stats'),
    path('admin/bulk-actions/', views.admin_bulk_actions, name='admin_bulk_actions'),

    # Property Management
    path('properties/', views.property_management, name='property_management'),
    path('properties/<int:property_id>/', views.property_detail, name='property_detail'),

]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)