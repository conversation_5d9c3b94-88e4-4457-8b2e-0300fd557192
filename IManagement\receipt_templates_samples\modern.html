<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Modern Receipt</title>
  <style>
    body {
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      max-width: 650px;
      margin: 20px auto;
      background: #fafafa;
      color: #2c3e50;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }
    header {
      border-bottom: 2px solid #3498db;
      padding-bottom: 15px;
      margin-bottom: 30px;
    }
    h1 {
      margin: 0;
      font-weight: 700;
      font-size: 2em;
      color: #2980b9;
    }
    .company {
      font-weight: 600;
      font-size: 1em;
      color: #7f8c8d;
    }
    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0 12px;
      margin-bottom: 25px;
    }
    th {
      text-align: left;
      color: #34495e;
      font-weight: 700;
      padding-bottom: 10px;
      border-bottom: 2px solid #bdc3c7;
    }
    td {
      background: #ecf0f1;
      padding: 12px 15px;
      border-radius: 8px;
    }
    .total {
      font-weight: 700;
      font-size: 1.4em;
      text-align: right;
      color: #27ae60;
      margin-bottom: 40px;
    }
    footer {
      font-size: 0.9em;
      color: #95a5a6;
      text-align: center;
    }
    .signature, .stamp {
      margin-top: 20px;
      font-weight: 600;
      border-top: 1px solid #bdc3c7;
      width: 250px;
      margin-left: auto;
      margin-right: auto;
      padding-top: 5px;
      color: #7f8c8d;
    }
  </style>
</head>
<body>
  <h1>Receipt</h1>
  <div class="company-info">
    <p>{{ company_name }}</p>
    <p>Thank you for your purchase!</p>
  </div>
  <table>
  <thead>
    <tr>
      <th>Item</th>
      <th>Qty</th>
      <th>Unit Price</th>
      <th>Price</th>
    </tr>
  </thead>
  <tbody>
    {% for item in items %}
    <tr>
      <td>{{ item.name }}</td>
      <td>{{ item.quantity }}</td>
      <td>{{ item.unit_price }}</td>
      <td>{{ item.total_price }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>

  <p class="total">Total: {{ total_price }}</p>
  <div class="footer">
    <p>Signature:</p>
    {% if signature_url %}
      <img src="{{ signature_url }}" alt="Signature" />
    {% else %}
      <p>____________________</p>
    {% endif %}

    <p>Stamp:</p>
    {% if stamp_url %}
      <img src="{{ stamp_url }}" alt="Stamp" />
    {% else %}
      <p>____________________</p>
    {% endif %}
  </div>
</body>
</html>
