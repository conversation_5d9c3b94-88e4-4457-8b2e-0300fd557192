<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Minimalist Receipt</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 500px;
      margin: 30px auto;
      color: #444;
      padding: 15px;
      background: #fff;
      border: 1px solid #ddd;
    }
    h2 {
      text-align: center;
      font-weight: 700;
      margin-bottom: 20px;
      color: #2c3e50;
    }
    .company {
      font-size: 0.95em;
      text-align: center;
      margin-bottom: 25px;
      color: #7f8c8d;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 25px;
    }
    th, td {
      border-bottom: 1px solid #eee;
      padding: 8px 6px;
      text-align: left;
    }
    th {
      font-weight: 700;
      color: #555;
    }
    .total {
      font-size: 1.3em;
      font-weight: 700;
      text-align: right;
      margin-top: 15px;
    }
    .sign-stamp {
      margin-top: 35px;
      display: flex;
      justify-content: space-between;
      font-size: 0.85em;
      color: #7f8c8d;
    }
    .sign-stamp div {
      border-top: 1px solid #ccc;
      width: 45%;
      text-align: center;
      padding-top: 5px;
    }
  </style>
</head>
<body>
  <h1>Receipt</h1>
  <div class="company-info">
    <p>{{ company_name }}</p>
    <p>Thank you for your purchase!</p>
  </div>
  <table>
  <thead>
    <tr>
      <th>Item</th>
      <th>Qty</th>
      <th>Unit Price</th>
      <th>Price</th>
    </tr>
  </thead>
  <tbody>
    {% for item in items %}
    <tr>
      <td>{{ item.name }}</td>
      <td>{{ item.quantity }}</td>
      <td>{{ item.unit_price }}</td>
      <td>{{ item.total_price }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>

  <p class="total">Total: {{ total_price }}</p>
  <div class="footer">
    <p>Signature:</p>
    {% if signature_url %}
      <img src="{{ signature_url }}" alt="Signature" />
    {% else %}
      <p>____________________</p>
    {% endif %}

    <p>Stamp:</p>
    {% if stamp_url %}
      <img src="{{ stamp_url }}" alt="Stamp" />
    {% else %}
      <p>____________________</p>
    {% endif %}
  </div>
</body>
</html>
