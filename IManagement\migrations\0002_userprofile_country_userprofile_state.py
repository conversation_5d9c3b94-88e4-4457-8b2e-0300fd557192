# Generated by Django 5.2.3 on 2025-07-23 16:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('IManagement', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='country',
            field=models.ForeignKey(blank=True, help_text="User's country during signup", null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.country'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='state',
            field=models.ForeignKey(blank=True, help_text="User's state during signup", null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.state'),
        ),
    ]
