# Generated by Django 5.2.1 on 2025-07-19 05:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=3)),
            ],
        ),
        migrations.CreateModel(
            name='InvoiceTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('html_content', models.TextField(blank=True, help_text='Full HTML template content')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ReceiptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('html_content', models.TextField(blank=True, help_text='Full HTML template content')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['created_at'],
                'unique_together': {('owner', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Goods',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(choices=[('NGN', '₦'), ('USD', '$'), ('EUR', '€'), ('GBP', '£')], default='NGN', max_length=3)),
                ('description', models.TextField(blank=True)),
                ('images', models.JSONField(blank=True, default=list)),
                ('is_used', models.BooleanField(default=False)),
                ('available_for_delivery', models.BooleanField(default=False)),
                ('delivery_type', models.CharField(blank=True, choices=[('within_state', 'Within State'), ('within_country', 'Within Country'), ('outside_country', 'Outside Country')], max_length=20, null=True)),
                ('available_for_bulk_sales', models.BooleanField(default=False)),
                ('quantity', models.PositiveIntegerField(default=0, help_text='Current stock quantity')),
                ('units_sold', models.PositiveIntegerField(default=0, help_text='Total units sold')),
                ('cost_price', models.DecimalField(blank=True, decimal_places=2, help_text='Cost price per unit', max_digits=10, null=True)),
                ('track_inventory', models.BooleanField(default=True, help_text='Enable inventory tracking')),
                ('low_stock_threshold', models.PositiveIntegerField(default=5, help_text='Alert when stock falls below this level')),
                ('sku', models.CharField(blank=True, help_text='Stock Keeping Unit', max_length=100, null=True)),
                ('star_rating', models.PositiveSmallIntegerField(blank=True, choices=[(1, '⭐ 1 Star'), (2, '⭐⭐ 2 Stars'), (3, '⭐⭐⭐ 3 Stars'), (4, '⭐⭐⭐⭐ 4 Stars'), (5, '⭐⭐⭐⭐⭐ 5 Stars')], help_text='Product rating (1-5 stars)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='goods', to='IManagement.category')),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.country')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='goods', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Goods',
                'verbose_name_plural': 'Goods',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('sale', 'Sale'), ('restock', 'Restock'), ('adjustment', 'Adjustment'), ('return', 'Return')], max_length=20)),
                ('quantity', models.IntegerField(help_text='Positive for additions, negative for deductions')),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('goods', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='IManagement.goods')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('issue_date', models.DateField()),
                ('due_date', models.DateField(blank=True, null=True)),
                ('client_name', models.CharField(max_length=255)),
                ('client_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('client_address', models.TextField(blank=True, null=True)),
                ('client_phone', models.CharField(blank=True, max_length=30, null=True)),
                ('currency', models.CharField(default='USD', max_length=5)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('tax', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_price', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('payment_terms', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('signature', models.ImageField(blank=True, null=True, upload_to='invoices/signatures/')),
                ('stamp', models.ImageField(blank=True, null=True, upload_to='invoices/stamps/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.invoicetemplate')),
            ],
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sku', models.CharField(blank=True, max_length=100, null=True)),
                ('name', models.CharField(max_length=255)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, editable=False, max_digits=12)),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='IManagement.invoice')),
            ],
        ),
        migrations.CreateModel(
            name='ManagedGood',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity', models.PositiveIntegerField(default=0)),
                ('units_sold', models.PositiveIntegerField(default=0)),
                ('category', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('signature', models.ImageField(blank=True, null=True, upload_to='receipts/signatures/')),
                ('stamp', models.ImageField(blank=True, null=True, upload_to='receipts/stamps/')),
                ('total_price', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('currency_symbol', models.CharField(default='$', max_length=5)),
                ('tax_amount', models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=10, null=True)),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=10, null=True)),
                ('client_name', models.CharField(blank=True, max_length=200, null=True)),
                ('client_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('client_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('client_address', models.TextField(blank=True, null=True)),
                ('client_company', models.CharField(blank=True, max_length=200, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='receipts', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.receipttemplate')),
            ],
        ),
        migrations.CreateModel(
            name='ReceiptItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, editable=False, max_digits=12)),
                ('receipt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='IManagement.receipt')),
            ],
        ),
        migrations.CreateModel(
            name='State',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='IManagement.country')),
            ],
        ),
        migrations.CreateModel(
            name='Property',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Property name or title', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the property')),
                ('property_type', models.CharField(choices=[('real_estate', 'Real Estate'), ('vehicle', 'Vehicle'), ('electronics', 'Electronics'), ('furniture', 'Furniture'), ('jewelry', 'Jewelry'), ('artwork', 'Artwork'), ('collectibles', 'Collectibles'), ('appliances', 'Appliances'), ('tools', 'Tools'), ('other', 'Other')], default='other', max_length=20)),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, help_text='Original purchase price', max_digits=12, null=True)),
                ('current_value', models.DecimalField(blank=True, decimal_places=2, help_text='Current estimated value', max_digits=12, null=True)),
                ('currency', models.CharField(choices=[('NGN', '₦'), ('USD', '$'), ('EUR', '€'), ('GBP', '£')], default='NGN', max_length=3)),
                ('condition', models.CharField(choices=[('excellent', 'Excellent'), ('very_good', 'Very Good'), ('good', 'Good'), ('fair', 'Fair'), ('poor', 'Poor')], default='good', max_length=20)),
                ('purchase_date', models.DateField(blank=True, help_text='Date when property was acquired', null=True)),
                ('warranty_expiry', models.DateField(blank=True, help_text='Warranty expiration date if applicable', null=True)),
                ('location', models.CharField(blank=True, help_text='Where the property is located/stored', max_length=255)),
                ('serial_number', models.CharField(blank=True, help_text='Serial number or unique identifier', max_length=100)),
                ('model_number', models.CharField(blank=True, help_text='Model number if applicable', max_length=100)),
                ('brand', models.CharField(blank=True, help_text='Brand or manufacturer', max_length=100)),
                ('insured', models.BooleanField(default=False, help_text='Is this property insured?')),
                ('insurance_value', models.DecimalField(blank=True, decimal_places=2, help_text='Insurance coverage amount', max_digits=12, null=True)),
                ('insurance_expiry', models.DateField(blank=True, help_text='Insurance expiration date', null=True)),
                ('images', models.JSONField(blank=True, default=list, help_text='List of image filenames')),
                ('documents', models.JSONField(blank=True, default=list, help_text='List of document filenames (receipts, warranties, etc.)')),
                ('notes', models.TextField(blank=True, help_text='Additional notes or comments')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags for easy searching', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.country')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='properties', to=settings.AUTH_USER_MODEL)),
                ('state', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.state')),
            ],
            options={
                'verbose_name': 'Property',
                'verbose_name_plural': 'Properties',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='goods',
            name='state',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='IManagement.state'),
        ),
        migrations.CreateModel(
            name='Store',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, default='')),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('authorized_users', models.ManyToManyField(blank=True, related_name='authorized_stores', to=settings.AUTH_USER_MODEL)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stores', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Store',
                'verbose_name_plural': 'Stores',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='goods',
            name='store',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='goods', to='IManagement.store'),
        ),
        migrations.CreateModel(
            name='StoreShare',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('share_token', models.CharField(help_text='Unique token for accessing shared store', max_length=64, unique=True)),
                ('title', models.CharField(blank=True, help_text='Custom title for the shared store', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Custom description for the shared store')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this share link is active')),
                ('expires_at', models.DateTimeField(blank=True, help_text='When this share link expires', null=True)),
                ('password_protected', models.BooleanField(default=False, help_text='Whether access requires a password')),
                ('access_password', models.CharField(blank=True, help_text='Password for accessing the shared store', max_length=128)),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of times this share has been viewed')),
                ('last_accessed', models.DateTimeField(blank=True, help_text='Last time this share was accessed', null=True)),
                ('shared_via_email', models.BooleanField(default=False, help_text='Whether this was shared via email')),
                ('recipient_emails', models.JSONField(blank=True, default=list, help_text='List of email addresses this was shared with')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_shares', to=settings.AUTH_USER_MODEL)),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shares', to='IManagement.store')),
            ],
            options={
                'verbose_name': 'Store Share',
                'verbose_name_plural': 'Store Shares',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('secondary_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('bio', models.TextField(blank=True, null=True)),
                ('website_url', models.URLField(blank=True, null=True)),
                ('company_name', models.CharField(blank=True, max_length=255, null=True)),
                ('verification_code', models.CharField(blank=True, max_length=6, null=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AlterUniqueTogether(
            name='goods',
            unique_together={('store', 'name')},
        ),
    ]
