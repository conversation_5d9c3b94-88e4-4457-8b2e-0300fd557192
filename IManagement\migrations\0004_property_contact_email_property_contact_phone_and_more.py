# Generated by Django 5.2.3 on 2025-07-26 05:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('IManagement', '0003_goodsactivity'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='contact_email',
            field=models.EmailField(blank=True, help_text='Contact email for inquiries', max_length=254),
        ),
        migrations.AddField(
            model_name='property',
            name='contact_phone',
            field=models.CharField(blank=True, help_text='Contact phone for inquiries', max_length=20),
        ),
        migrations.AddField(
            model_name='property',
            name='for_sale',
            field=models.BooleanField(default=False, help_text='Is this property available for sale?'),
        ),
        migrations.AddField(
            model_name='property',
            name='is_negotiable',
            field=models.BooleanField(default=True, help_text='Is the price negotiable?'),
        ),
        migrations.AddField(
            model_name='property',
            name='sale_description',
            field=models.TextField(blank=True, help_text='Additional description for sale listing'),
        ),
        migrations.AddField(
            model_name='property',
            name='sale_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Asking price for sale', max_digits=12, null=True),
        ),
        migrations.CreateModel(
            name='SupportTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(help_text='Contact email', max_length=254)),
                ('name', models.CharField(help_text='Contact name', max_length=255)),
                ('subject', models.CharField(help_text='Ticket subject', max_length=255)),
                ('message', models.TextField(help_text='Detailed message')),
                ('category', models.CharField(choices=[('technical', 'Technical Issue'), ('account', 'Account Issue'), ('billing', 'Billing Issue'), ('feature', 'Feature Request'), ('bug', 'Bug Report'), ('other', 'Other')], default='other', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('status', models.CharField(choices=[('open', 'Open'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed')], default='open', max_length=20)),
                ('admin_notes', models.TextField(blank=True, help_text='Internal admin notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tickets', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='support_tickets', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Support Ticket',
                'verbose_name_plural': 'Support Tickets',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TrustedDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_token', models.CharField(help_text='Unique token for this device', max_length=255, unique=True)),
                ('device_name', models.CharField(help_text='Human-readable device name', max_length=255)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_used', models.DateTimeField(auto_now=True)),
                ('expires_at', models.DateTimeField(help_text='When this trust expires (7 days)')),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trusted_devices', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Trusted Device',
                'verbose_name_plural': 'Trusted Devices',
                'ordering': ['-last_used'],
            },
        ),
        migrations.CreateModel(
            name='TwoFactorAuth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text='6-digit verification code', max_length=6)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(help_text='When this code expires')),
                ('is_used', models.BooleanField(default=False)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='two_factor_codes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Two Factor Authentication',
                'verbose_name_plural': 'Two Factor Authentications',
                'ordering': ['-created_at'],
            },
        ),
    ]
