{% extends 'base.html' %}

{% block title %}{{ property.name }} - Property Details{% endblock %}

{% block extra_css %}
<style>
    .property-detail-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .property-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .property-type-badge {
        background: rgba(255,255,255,0.2);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin-top: 10px;
        display: inline-block;
    }
    
    .detail-section {
        padding: 25px;
        border-bottom: 1px solid #eee;
    }
    
    .detail-section:last-child {
        border-bottom: none;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .detail-label {
        font-weight: 600;
        color: #555;
        flex: 1;
    }
    
    .detail-value {
        flex: 2;
        text-align: right;
    }
    
    .value-highlight {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .tag {
        display: inline-block;
        background: #007bff;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin: 2px;
    }
    
    .action-buttons {
        padding: 25px;
        background: #f8f9fa;
        text-align: center;
    }
    
    .btn-action {
        margin: 0 10px;
        min-width: 120px;
    }
    
    .value-change {
        font-weight: 600;
    }
    
    .value-increase {
        color: #28a745;
    }
    
    .value-decrease {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Back Button -->
    <div class="mb-4">
        <a href="{% url 'property_management' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Properties
        </a>
    </div>
    
    <!-- Property Detail Card -->
    <div class="property-detail-card">
        <!-- Header -->
        <div class="property-header">
            <h1 class="mb-2">{{ property.name }}</h1>
            <div class="property-type-badge">
                {{ property.get_property_type_display }}
            </div>
        </div>

        <!-- Property Images -->
        {% if property.images %}
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-images text-primary"></i> Property Images</h4>

            <div class="row">
                {% for image_url in property.images %}
                <div class="col-md-4 col-sm-6 mb-3">
                    <div class="image-container">
                        <img src="{{ image_url }}"
                             class="img-fluid rounded shadow-sm"
                             alt="{{ property.name }}"
                             style="width: 100%; height: 200px; object-fit: cover; cursor: pointer;"
                             onclick="openImageModal('{{ image_url }}', '{{ property.name }}')">
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Sale Information -->
        {% if property.for_sale %}
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-store text-success"></i> Sale Information</h4>

            <div class="detail-row">
                <div class="detail-label">Sale Price</div>
                <div class="detail-value">
                    <span class="price-display text-success fw-bold fs-4">
                        ${{ property.sale_price|floatformat:2 }}
                        {% if property.is_negotiable %}
                            <small class="text-muted">(Negotiable)</small>
                        {% endif %}
                    </span>
                </div>
            </div>

            {% if property.sale_description %}
            <div class="detail-row">
                <div class="detail-label">Sale Description</div>
                <div class="detail-value">{{ property.sale_description }}</div>
            </div>
            {% endif %}

            {% if property.contact_phone %}
            <div class="detail-row">
                <div class="detail-label">Contact Phone</div>
                <div class="detail-value">
                    <a href="tel:{{ property.contact_phone }}" class="text-decoration-none">
                        <i class="fas fa-phone text-success"></i> {{ property.contact_phone }}
                    </a>
                </div>
            </div>
            {% endif %}

            {% if property.contact_email %}
            <div class="detail-row">
                <div class="detail-label">Contact Email</div>
                <div class="detail-value">
                    <a href="mailto:{{ property.contact_email }}" class="text-decoration-none">
                        <i class="fas fa-envelope text-primary"></i> {{ property.contact_email }}
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Basic Information -->
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-info-circle text-primary"></i> Basic Information</h4>
            
            {% if property.description %}
            <div class="detail-row">
                <div class="detail-label">Description</div>
                <div class="detail-value">{{ property.description }}</div>
            </div>
            {% endif %}
            
            <div class="detail-row">
                <div class="detail-label">Condition</div>
                <div class="detail-value">
                    <span class="status-badge 
                        {% if property.condition == 'excellent' %}bg-success text-white
                        {% elif property.condition == 'very_good' %}bg-info text-white
                        {% elif property.condition == 'good' %}bg-primary text-white
                        {% elif property.condition == 'fair' %}bg-warning text-dark
                        {% else %}bg-danger text-white{% endif %}">
                        {{ property.get_condition_display }}
                    </span>
                </div>
            </div>
            
            {% if property.brand %}
            <div class="detail-row">
                <div class="detail-label">Brand</div>
                <div class="detail-value">{{ property.brand }}</div>
            </div>
            {% endif %}
            
            {% if property.model_number %}
            <div class="detail-row">
                <div class="detail-label">Model Number</div>
                <div class="detail-value">{{ property.model_number }}</div>
            </div>
            {% endif %}
            
            {% if property.serial_number %}
            <div class="detail-row">
                <div class="detail-label">Serial Number</div>
                <div class="detail-value">{{ property.serial_number }}</div>
            </div>
            {% endif %}
            
            {% if property.location %}
            <div class="detail-row">
                <div class="detail-label">Location</div>
                <div class="detail-value">{{ property.location }}</div>
            </div>
            {% endif %}
        </div>
        
        <!-- Financial Information -->
        {% if property.purchase_price or property.current_value %}
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-dollar-sign text-success"></i> Financial Information</h4>
            
            {% if property.purchase_price %}
            <div class="detail-row">
                <div class="detail-label">Purchase Price</div>
                <div class="detail-value value-highlight">{{ property.currency }} {{ property.purchase_price|floatformat:2 }}</div>
            </div>
            {% endif %}
            
            {% if property.current_value %}
            <div class="detail-row">
                <div class="detail-label">Current Value</div>
                <div class="detail-value value-highlight">{{ property.currency }} {{ property.current_value|floatformat:2 }}</div>
            </div>
            {% endif %}
            
            {% if property.get_value_change_percentage %}
            <div class="detail-row">
                <div class="detail-label">Value Change</div>
                <div class="detail-value">
                    <span class="value-change {% if property.get_value_change_percentage > 0 %}value-increase{% else %}value-decrease{% endif %}">
                        {% if property.get_value_change_percentage > 0 %}+{% endif %}{{ property.get_value_change_percentage }}%
                    </span>
                </div>
            </div>
            {% endif %}
            
            {% if property.purchase_date %}
            <div class="detail-row">
                <div class="detail-label">Purchase Date</div>
                <div class="detail-value">{{ property.purchase_date|date:"F d, Y" }}</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        <!-- Insurance Information -->
        {% if property.insured %}
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-shield-alt text-info"></i> Insurance Information</h4>
            
            <div class="detail-row">
                <div class="detail-label">Insurance Status</div>
                <div class="detail-value">
                    <span class="status-badge {% if property.is_insurance_valid %}bg-success text-white{% else %}bg-danger text-white{% endif %}">
                        {% if property.is_insurance_valid %}Active{% else %}Expired{% endif %}
                    </span>
                </div>
            </div>
            
            {% if property.insurance_value %}
            <div class="detail-row">
                <div class="detail-label">Insurance Value</div>
                <div class="detail-value value-highlight">{{ property.currency }} {{ property.insurance_value|floatformat:2 }}</div>
            </div>
            {% endif %}
            
            {% if property.insurance_expiry %}
            <div class="detail-row">
                <div class="detail-label">Insurance Expiry</div>
                <div class="detail-value">{{ property.insurance_expiry|date:"F d, Y" }}</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        <!-- Warranty Information -->
        {% if property.warranty_expiry %}
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-tools text-warning"></i> Warranty Information</h4>
            
            <div class="detail-row">
                <div class="detail-label">Warranty Status</div>
                <div class="detail-value">
                    <span class="status-badge {% if property.is_warranty_valid %}bg-success text-white{% else %}bg-danger text-white{% endif %}">
                        {% if property.is_warranty_valid %}Valid{% else %}Expired{% endif %}
                    </span>
                </div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Warranty Expiry</div>
                <div class="detail-value">{{ property.warranty_expiry|date:"F d, Y" }}</div>
            </div>
        </div>
        {% endif %}
        
        <!-- Tags -->
        {% if property.get_tags_list %}
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-tags text-secondary"></i> Tags</h4>
            <div>
                {% for tag in property.get_tags_list %}
                    <span class="tag">{{ tag }}</span>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Notes -->
        {% if property.notes %}
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-sticky-note text-info"></i> Notes</h4>
            <p class="mb-0">{{ property.notes|linebreaks }}</p>
        </div>
        {% endif %}
        
        <!-- Metadata -->
        <div class="detail-section">
            <h4 class="mb-4"><i class="fas fa-clock text-muted"></i> Record Information</h4>
            
            <div class="detail-row">
                <div class="detail-label">Created</div>
                <div class="detail-value">{{ property.created_at|date:"F d, Y g:i A" }}</div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Last Updated</div>
                <div class="detail-value">{{ property.updated_at|date:"F d, Y g:i A" }}</div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-primary btn-action" onclick="editProperty()">
                <i class="fas fa-edit"></i> Edit Property
            </button>
            <button class="btn btn-danger btn-action" onclick="deleteProperty()">
                <i class="fas fa-trash"></i> Delete Property
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editProperty() {
    // TODO: Implement edit functionality
    alert('Edit functionality coming soon!');
}

function deleteProperty() {
    if (confirm('Are you sure you want to delete "{{ property.name }}"? This action cannot be undone.')) {
        fetch(`/properties/{{ property.id }}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{% url "property_management" %}';
            } else {
                alert('Error: ' + (data.error || 'Failed to delete property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the property');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Image modal functionality
function openImageModal(imageUrl, altText) {
    const modalHTML = `
        <div class="modal fade" id="imageModal" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${altText}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imageUrl}" class="img-fluid" alt="${altText}" style="max-height: 70vh;">
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('imageModal');
    if (existingModal) existingModal.remove();

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    modal.show();
}
</script>
{% endblock %}
